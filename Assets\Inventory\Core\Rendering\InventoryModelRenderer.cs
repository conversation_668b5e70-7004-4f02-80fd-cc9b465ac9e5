using UnityEngine;
using UnityEngine.Rendering;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Centralized system for rendering 3D models in an isolated area for inventory UI texture generation.
/// This system creates a dedicated rendering space away from the main game world to avoid interference.
/// </summary>
public class InventoryModelRenderer : MonoBehaviour, IInventoryRenderer
{
    [Header("Rendering Configuration")]
    [SerializeField] private Vector3 renderAreaPosition = new Vector3(10000, 10000, 10000);
    [SerializeField] private int renderTextureSize = 256;
    [SerializeField] private RenderTextureFormat renderTextureFormat = RenderTextureFormat.ARGB32;
    [SerializeField] private int renderTextureDepth = 24;
    
    [Header("Camera Settings")]
    [SerializeField] private float cameraFieldOfView = 60f;
    [SerializeField] private float cameraThreshold = 0.1f;
    [SerializeField] private Color backgroundColor = Color.clear;
    
    [Header("Lighting")]
    [SerializeField] private Light renderLight;
    [SerializeField] private LightType lightType = LightType.Directional;
    [SerializeField] private Color lightColor = Color.white;
    [SerializeField] private float lightIntensity = 1f;
    [SerializeField] private Vector3 lightRotation = new Vector3(50f, -30f, 0f);
    
    // Singleton instance
    private static InventoryModelRenderer _instance;
    public static InventoryModelRenderer Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<InventoryModelRenderer>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("InventoryModelRenderer");
                    _instance = go.AddComponent<InventoryModelRenderer>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }
    
    // Core components
    private Camera renderCamera;
    private Transform renderArea;
    private Transform modelContainer;
    
    // Advanced render texture cache
    private InventoryRenderCache renderCache;
    
    // Rendering queue with priority support
    private Queue<RenderRequest> renderQueue = new Queue<RenderRequest>();
    private Queue<RenderRequest> priorityQueue = new Queue<RenderRequest>();
    private bool isRendering = false;

    // Performance monitoring
    private int totalRendersThisFrame = 0;
    private const int maxRendersPerFrame = 3;

    public enum RenderPriority
    {
        Normal,
        High
    }

    private struct RenderRequest
    {
        public ItemSO itemSO;
        public System.Action<RenderTexture> onComplete;
        public bool useCache;
        public RenderPriority priority;
        public float requestTime;

        public RenderRequest(ItemSO item, System.Action<RenderTexture> callback, bool cache = true, RenderPriority priority = RenderPriority.Normal)
        {
            itemSO = item;
            onComplete = callback;
            useCache = cache;
            this.priority = priority;
            requestTime = Time.time;
        }
    }
    
    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeRenderingSystem();
            InitializeCache();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    /// <summary>
    /// Initializes the isolated rendering system with camera, lighting, and render area.
    /// </summary>
    private void InitializeRenderingSystem()
    {
        // Find optimal position for render area
        renderAreaPosition = InventoryRenderAreaSetup.FindOptimalRenderPosition();

        // Create render area container
        GameObject renderAreaGO = new GameObject("InventoryRenderArea");
        renderAreaGO.transform.position = renderAreaPosition;
        renderAreaGO.transform.SetParent(transform);
        renderArea = renderAreaGO.transform;

        // Create model container within render area
        GameObject modelContainerGO = new GameObject("ModelContainer");
        modelContainerGO.transform.SetParent(renderArea);
        modelContainerGO.transform.localPosition = Vector3.zero;
        modelContainer = modelContainerGO.transform;

        // Create and configure camera
        GameObject cameraGO = new GameObject("RenderCamera");
        cameraGO.transform.SetParent(renderArea);
        renderCamera = cameraGO.AddComponent<Camera>();

        ConfigureCamera();
        SetupLighting();

        Debug.Log($"InventoryModelRenderer initialized at position {renderAreaPosition}");
    }

    /// <summary>
    /// Initializes the advanced caching system.
    /// </summary>
    private void InitializeCache()
    {
        // Initialize with reasonable defaults
        renderCache = new InventoryRenderCache(
            maxSize: 100,           // Cache up to 100 items
            expirationTime: 300f,   // 5 minutes expiration
            persistent: false       // Disable persistent cache by default
        );
    }
    
    /// <summary>
    /// Configures the render camera with appropriate settings for inventory item rendering.
    /// </summary>
    private void ConfigureCamera()
    {
        var config = InventoryRenderAreaSetup.GetRecommendedCameraConfig();

        renderCamera.clearFlags = config.clearFlags;
        renderCamera.backgroundColor = backgroundColor;
        renderCamera.fieldOfView = config.fieldOfView;
        renderCamera.nearClipPlane = config.nearClipPlane;
        renderCamera.farClipPlane = config.farClipPlane;
        renderCamera.enabled = false; // Only enable when rendering
        renderCamera.cullingMask = config.cullingMask;
        renderCamera.renderingPath = config.renderingPath;
    }
    
    /// <summary>
    /// Sets up lighting for the isolated render area.
    /// </summary>
    private void SetupLighting()
    {
        // Setup main lighting using the utility
        renderLight = InventoryRenderAreaSetup.SetupOptimalLighting(renderArea);

        // Optionally setup fill lighting for better item visibility
        InventoryRenderAreaSetup.SetupFillLighting(renderArea, renderLight);

        // Setup background if needed
        InventoryRenderAreaSetup.SetupRenderBackground(renderArea, backgroundColor);
    }
    
    /// <summary>
    /// Requests rendering of an item model to a render texture.
    /// </summary>
    /// <param name="itemSO">The item to render</param>
    /// <param name="onComplete">Callback when rendering is complete</param>
    /// <param name="useCache">Whether to use cached textures if available</param>
    /// <param name="priority">Rendering priority for the request</param>
    public void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, bool useCache = true, RenderPriority priority = RenderPriority.Normal)
    {
        if (itemSO == null || itemSO.prefab == null)
        {
            Debug.LogWarning("Cannot render null item or item without prefab");
            onComplete?.Invoke(null);
            return;
        }

        // Check cache first if enabled
        if (useCache && renderCache.Contains(itemSO))
        {
            RenderTexture cachedTexture = renderCache.Get(itemSO);
            if (cachedTexture != null)
            {
                onComplete?.Invoke(cachedTexture);
                return;
            }
        }

        // Add to appropriate queue based on priority
        RenderRequest request = new RenderRequest(itemSO, onComplete, useCache, priority);

        if (priority == RenderPriority.High)
        {
            priorityQueue.Enqueue(request);
        }
        else
        {
            renderQueue.Enqueue(request);
        }

        // Start processing if not already rendering
        if (!isRendering)
        {
            StartCoroutine(ProcessRenderQueue());
        }
    }

    /// <summary>
    /// Overload for interface compatibility.
    /// </summary>
    public void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, bool useCache = true)
    {
        RequestRender(itemSO, onComplete, useCache, RenderPriority.Normal);
    }
    
    /// <summary>
    /// Processes the render queue sequentially to avoid conflicts.
    /// Priority queue is processed first.
    /// </summary>
    private IEnumerator ProcessRenderQueue()
    {
        isRendering = true;
        totalRendersThisFrame = 0;

        while ((priorityQueue.Count > 0 || renderQueue.Count > 0) && totalRendersThisFrame < maxRendersPerFrame)
        {
            RenderRequest request;

            // Process priority queue first
            if (priorityQueue.Count > 0)
            {
                request = priorityQueue.Dequeue();
            }
            else
            {
                request = renderQueue.Dequeue();
            }

            yield return StartCoroutine(RenderItemModel(request));
            totalRendersThisFrame++;

            // Yield control if we've hit the frame limit
            if (totalRendersThisFrame >= maxRendersPerFrame)
            {
                yield return null; // Wait for next frame
                totalRendersThisFrame = 0;
            }
        }

        isRendering = false;

        // If there are still items in queue, continue processing next frame
        if (priorityQueue.Count > 0 || renderQueue.Count > 0)
        {
            StartCoroutine(ProcessRenderQueue());
        }
    }
    
    /// <summary>
    /// Renders a single item model to a render texture.
    /// </summary>
    private IEnumerator RenderItemModel(RenderRequest request)
    {
        GameObject modelInstance = null;
        RenderTexture renderTexture = null;
        
        try
        {
            // Create render texture
            renderTexture = new RenderTexture(renderTextureSize, renderTextureSize, renderTextureDepth, renderTextureFormat);
            renderTexture.name = $"ItemTexture_{request.itemSO.name}";
            renderCamera.targetTexture = renderTexture;
            
            // Instantiate model in isolated area
            modelInstance = Instantiate(request.itemSO.prefab, modelContainer);
            
            // Configure model for rendering (disable physics, etc.)
            ConfigureModelForRendering(modelInstance);
            
            // Position camera to frame the model
            CameraUtils.FitOnCamera(modelInstance, renderCamera, cameraThreshold);
            
            // Enable camera and render
            renderCamera.enabled = true;
            renderCamera.Render();
            renderCamera.enabled = false;
            
            // Cache the result if requested
            if (request.useCache)
            {
                renderCache.Add(request.itemSO, renderTexture, request.priority == RenderPriority.High);
            }
            
            // Callback with result
            request.onComplete?.Invoke(renderTexture);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error rendering item {request.itemSO.name}: {e.Message}");
            request.onComplete?.Invoke(null);
        }
        finally
        {
            // Clean up model instance
            if (modelInstance != null)
            {
                DestroyImmediate(modelInstance);
            }
            
            // Reset camera
            renderCamera.targetTexture = null;
        }
        
        yield return null; // Wait one frame between renders
    }
    
    /// <summary>
    /// Configures a model instance for rendering by disabling physics and colliders.
    /// </summary>
    private void ConfigureModelForRendering(GameObject model)
    {
        // Disable all rigidbodies
        Rigidbody[] rigidbodies = model.GetComponentsInChildren<Rigidbody>();
        foreach (var rb in rigidbodies)
        {
            rb.isKinematic = true;
        }
        
        // Disable all colliders
        Collider[] colliders = model.GetComponentsInChildren<Collider>();
        foreach (var col in colliders)
        {
            col.enabled = false;
        }
        
        // Ensure model is on correct layer if needed
        // SetLayerRecursively(model, LayerMask.NameToLayer("Default"));
    }
    
    /// <summary>
    /// Clears the render texture cache.
    /// </summary>
    public void ClearCache()
    {
        renderCache?.Clear();
    }
    
    /// <summary>
    /// Gets a cached render texture for an item if available.
    /// </summary>
    public RenderTexture GetCachedTexture(ItemSO itemSO)
    {
        return renderCache?.Get(itemSO);
    }

    /// <summary>
    /// Checks if an item has a cached render texture.
    /// </summary>
    public bool HasCachedTexture(ItemSO itemSO)
    {
        return renderCache?.Contains(itemSO) ?? false;
    }

    /// <summary>
    /// Gets the current queue size for monitoring performance.
    /// </summary>
    public int GetQueueSize()
    {
        return renderQueue.Count + priorityQueue.Count;
    }

    /// <summary>
    /// Gets the current priority queue size.
    /// </summary>
    public int GetPriorityQueueSize()
    {
        return priorityQueue.Count;
    }

    /// <summary>
    /// Gets the current normal queue size.
    /// </summary>
    public int GetNormalQueueSize()
    {
        return renderQueue.Count;
    }

    /// <summary>
    /// Gets whether the renderer is currently processing requests.
    /// </summary>
    public bool IsRendering()
    {
        return isRendering;
    }

    /// <summary>
    /// Updates render area position (useful for avoiding conflicts with game world).
    /// </summary>
    public void SetRenderAreaPosition(Vector3 newPosition)
    {
        renderAreaPosition = newPosition;
        if (renderArea != null)
        {
            renderArea.position = newPosition;
        }
    }

    /// <summary>
    /// Updates render texture settings. Clears cache to ensure consistency.
    /// </summary>
    public void UpdateRenderSettings(int textureSize, RenderTextureFormat format = RenderTextureFormat.ARGB32)
    {
        if (textureSize != renderTextureSize || format != renderTextureFormat)
        {
            renderTextureSize = textureSize;
            renderTextureFormat = format;
            ClearCache(); // Clear cache since texture format changed
        }
    }

    /// <summary>
    /// Gets cache statistics for monitoring performance.
    /// </summary>
    public InventoryRenderCache.CacheStats GetCacheStats()
    {
        return renderCache?.GetStats() ?? new InventoryRenderCache.CacheStats();
    }

    /// <summary>
    /// Preloads render textures for a list of items.
    /// Useful for preloading commonly used items.
    /// </summary>
    public void PreloadItems(ItemSO[] items, System.Action onComplete = null)
    {
        if (items == null || items.Length == 0)
        {
            onComplete?.Invoke();
            return;
        }

        int remainingItems = items.Length;

        foreach (ItemSO item in items)
        {
            if (item != null && !HasCachedTexture(item))
            {
                RequestRender(item, (texture) =>
                {
                    remainingItems--;
                    if (remainingItems <= 0)
                    {
                        onComplete?.Invoke();
                    }
                }, useCache: true, priority: RenderPriority.Normal);
            }
            else
            {
                remainingItems--;
                if (remainingItems <= 0)
                {
                    onComplete?.Invoke();
                }
            }
        }
    }

    /// <summary>
    /// Gets the total number of items in the render queue.
    /// </summary>
    public int GetQueueSize()
    {
        return renderQueue.Count;
    }

    /// <summary>
    /// Gets the number of high priority items in the queue.
    /// </summary>
    public int GetPriorityQueueSize()
    {
        int count = 0;
        foreach (var request in renderQueue)
        {
            if (request.priority == RenderPriority.High)
                count++;
        }
        return count;
    }

    /// <summary>
    /// Gets the number of normal priority items in the queue.
    /// </summary>
    public int GetNormalQueueSize()
    {
        return GetQueueSize() - GetPriorityQueueSize();
    }

    /// <summary>
    /// Sets the render area position.
    /// </summary>
    public void SetRenderAreaPosition(Vector3 position)
    {
        renderAreaPosition = position;
        if (renderCamera != null)
        {
            renderCamera.transform.position = position + Vector3.back * 5f;
        }
    }

    void OnDestroy()
    {
        ClearCache();
    }
}
