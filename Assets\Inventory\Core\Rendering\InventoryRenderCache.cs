using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// Advanced caching system for inventory item render textures.
/// Supports persistent caching, memory management, and cache validation.
/// </summary>
public class InventoryRenderCache
{
    private Dictionary<int, CacheEntry> cache = new Dictionary<int, CacheEntry>();
    private Dictionary<int, float> accessTimes = new Dictionary<int, float>();
    
    // Cache settings
    private int maxCacheSize = 100;
    private float cacheExpirationTime = 300f; // 5 minutes
    private bool enablePersistentCache = false;
    private string persistentCachePath;
    
    // Memory management
    private long maxMemoryUsage = 100 * 1024 * 1024; // 100MB
    private long currentMemoryUsage = 0;
    
    [System.Serializable]
    private struct CacheEntry
    {
        public RenderTexture texture;
        public float creationTime;
        public int itemID;
        public string itemName;
        public long memorySize;
        public bool isPersistent;
        
        public CacheEntry(RenderTexture tex, int id, string name, bool persistent = false)
        {
            texture = tex;
            creationTime = Time.time;
            itemID = id;
            itemName = name;
            isPersistent = persistent;
            
            // Calculate approximate memory usage
            if (tex != null)
            {
                memorySize = tex.width * tex.height * 4; // RGBA32 = 4 bytes per pixel
            }
            else
            {
                memorySize = 0;
            }
        }
    }
    
    public InventoryRenderCache(int maxSize = 100, float expirationTime = 300f, bool persistent = false)
    {
        maxCacheSize = maxSize;
        cacheExpirationTime = expirationTime;
        enablePersistentCache = persistent;
        
        if (enablePersistentCache)
        {
            persistentCachePath = Path.Combine(Application.persistentDataPath, "InventoryCache");
            Directory.CreateDirectory(persistentCachePath);
        }
    }
    
    /// <summary>
    /// Adds a render texture to the cache.
    /// </summary>
    public void Add(ItemSO itemSO, RenderTexture texture, bool persistent = false)
    {
        if (itemSO == null || texture == null)
            return;
            
        // Check if we need to make room
        if (cache.Count >= maxCacheSize)
        {
            EvictOldestEntry();
        }
        
        // Check memory usage
        long textureMemory = texture.width * texture.height * 4;
        if (currentMemoryUsage + textureMemory > maxMemoryUsage)
        {
            EvictByMemoryPressure(textureMemory);
        }
        
        // Remove existing entry if present
        if (cache.ContainsKey(itemSO.ID))
        {
            Remove(itemSO.ID);
        }
        
        // Add new entry
        CacheEntry entry = new CacheEntry(texture, itemSO.ID, itemSO.name, persistent);
        cache[itemSO.ID] = entry;
        accessTimes[itemSO.ID] = Time.time;
        currentMemoryUsage += entry.memorySize;
        
        // Save to persistent cache if enabled
        if (persistent && enablePersistentCache)
        {
            SaveToPersistentCache(itemSO, texture);
        }
    }
    
    /// <summary>
    /// Gets a cached render texture for an item.
    /// </summary>
    public RenderTexture Get(ItemSO itemSO)
    {
        if (itemSO == null)
            return null;
            
        return Get(itemSO.ID);
    }
    
    /// <summary>
    /// Gets a cached render texture by item ID.
    /// </summary>
    public RenderTexture Get(int itemID)
    {
        if (cache.TryGetValue(itemID, out CacheEntry entry))
        {
            // Check if entry has expired
            if (Time.time - entry.creationTime > cacheExpirationTime && !entry.isPersistent)
            {
                Remove(itemID);
                return null;
            }
            
            // Update access time
            accessTimes[itemID] = Time.time;
            return entry.texture;
        }
        
        // Try to load from persistent cache
        if (enablePersistentCache)
        {
            return LoadFromPersistentCache(itemID);
        }
        
        return null;
    }
    
    /// <summary>
    /// Checks if an item is cached.
    /// </summary>
    public bool Contains(ItemSO itemSO)
    {
        return itemSO != null && Contains(itemSO.ID);
    }
    
    /// <summary>
    /// Checks if an item ID is cached.
    /// </summary>
    public bool Contains(int itemID)
    {
        if (cache.TryGetValue(itemID, out CacheEntry entry))
        {
            // Check expiration
            if (Time.time - entry.creationTime > cacheExpirationTime && !entry.isPersistent)
            {
                Remove(itemID);
                return false;
            }
            return true;
        }
        return false;
    }
    
    /// <summary>
    /// Removes an item from the cache.
    /// </summary>
    public void Remove(int itemID)
    {
        if (cache.TryGetValue(itemID, out CacheEntry entry))
        {
            if (entry.texture != null)
            {
                entry.texture.Release();
                Object.DestroyImmediate(entry.texture);
            }
            
            currentMemoryUsage -= entry.memorySize;
            cache.Remove(itemID);
            accessTimes.Remove(itemID);
        }
    }
    
    /// <summary>
    /// Clears all cached textures.
    /// </summary>
    public void Clear()
    {
        foreach (var entry in cache.Values)
        {
            if (entry.texture != null)
            {
                entry.texture.Release();
                Object.DestroyImmediate(entry.texture);
            }
        }
        
        cache.Clear();
        accessTimes.Clear();
        currentMemoryUsage = 0;
    }
    
    /// <summary>
    /// Evicts the oldest cache entry.
    /// </summary>
    private void EvictOldestEntry()
    {
        float oldestTime = float.MaxValue;
        int oldestID = -1;
        
        foreach (var kvp in accessTimes)
        {
            if (kvp.Value < oldestTime && !cache[kvp.Key].isPersistent)
            {
                oldestTime = kvp.Value;
                oldestID = kvp.Key;
            }
        }
        
        if (oldestID != -1)
        {
            Remove(oldestID);
        }
    }
    
    /// <summary>
    /// Evicts entries to free up memory.
    /// </summary>
    private void EvictByMemoryPressure(long requiredMemory)
    {
        var sortedEntries = new List<KeyValuePair<int, float>>(accessTimes);
        sortedEntries.Sort((a, b) => a.Value.CompareTo(b.Value));
        
        foreach (var kvp in sortedEntries)
        {
            if (currentMemoryUsage + requiredMemory <= maxMemoryUsage)
                break;
                
            if (!cache[kvp.Key].isPersistent)
            {
                Remove(kvp.Key);
            }
        }
    }
    
    /// <summary>
    /// Saves a texture to persistent cache.
    /// </summary>
    private void SaveToPersistentCache(ItemSO itemSO, RenderTexture texture)
    {
        try
        {
            string filename = $"item_{itemSO.ID}.png";
            string filepath = Path.Combine(persistentCachePath, filename);
            
            // Convert RenderTexture to Texture2D and save as PNG
            RenderTexture.active = texture;
            Texture2D tex2D = new Texture2D(texture.width, texture.height, TextureFormat.RGBA32, false);
            tex2D.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
            tex2D.Apply();
            RenderTexture.active = null;
            
            byte[] pngData = tex2D.EncodeToPNG();
            File.WriteAllBytes(filepath, pngData);
            
            Object.DestroyImmediate(tex2D);
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to save persistent cache for item {itemSO.name}: {e.Message}");
        }
    }
    
    /// <summary>
    /// Loads a texture from persistent cache.
    /// </summary>
    private RenderTexture LoadFromPersistentCache(int itemID)
    {
        try
        {
            string filename = $"item_{itemID}.png";
            string filepath = Path.Combine(persistentCachePath, filename);
            
            if (File.Exists(filepath))
            {
                byte[] pngData = File.ReadAllBytes(filepath);
                Texture2D tex2D = new Texture2D(2, 2);
                tex2D.LoadImage(pngData);
                
                // Convert back to RenderTexture
                RenderTexture renderTex = new RenderTexture(tex2D.width, tex2D.height, 24, RenderTextureFormat.ARGB32);
                Graphics.Blit(tex2D, renderTex);
                
                Object.DestroyImmediate(tex2D);
                return renderTex;
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Failed to load persistent cache for item {itemID}: {e.Message}");
        }
        
        return null;
    }
    
    /// <summary>
    /// Gets cache statistics for monitoring.
    /// </summary>
    public CacheStats GetStats()
    {
        return new CacheStats
        {
            entryCount = cache.Count,
            memoryUsage = currentMemoryUsage,
            maxMemoryUsage = maxMemoryUsage,
            hitRate = CalculateHitRate()
        };
    }
    
    private float CalculateHitRate()
    {
        // This would need to be tracked over time for accurate hit rate calculation
        // For now, return a placeholder
        return cache.Count > 0 ? 0.8f : 0f;
    }
    
    [System.Serializable]
    public struct CacheStats
    {
        public int entryCount;
        public long memoryUsage;
        public long maxMemoryUsage;
        public float hitRate;
    }
}
