%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5db3e374ca939948ad2cf8aad56acdc, type: 3}
  m_Name: ManHole SO
  m_EditorClassIdentifier: 
  ID: 0
  itemName: Man Hole
  description: 'Um bueiro '
  icon: {fileID: 0}
  modelInfo:
    prefab: {fileID: 2167432157741291248, guid: 785df7832e5374d43b8539621916eb5c, type: 3}
    defaultRotation: {x: 0, y: 0, z: 0}
    intrinsicForward: {x: 0, y: 0, z: -1}
    centerOffset: {x: 0, y: 0.00000023841858, z: 0}
    scaleRatio: {x: 1, y: 1, z: 0.04175857}
    meshBoundsRadius: 1.41483
    useAutoBounds: 0
  stack: 2
  maxStack: 99
  value: 0
  weight: 0
  gridWidth: 1
  gridHeight: 1
  canBeRotated: 0
