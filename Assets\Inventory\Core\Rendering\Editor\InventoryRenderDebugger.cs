using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

#if UNITY_EDITOR

/// <summary>
/// Editor window for debugging and testing the inventory rendering system.
/// Provides tools for monitoring performance, testing renders, and debugging issues.
/// </summary>
public class InventoryRenderDebugger : EditorWindow
{
    private Vector2 scrollPosition;
    private ItemSO testItem;
    private RenderTexture previewTexture;
    private bool autoRefresh = true;
    private float refreshInterval = 1f;
    private double lastRefreshTime;
    
    // Tabs
    private int selectedTab = 0;
    private readonly string[] tabNames = { "Monitor", "Test Render", "Cache", "Settings" };
    
    // Cache monitoring
    private InventoryRenderCache.CacheStats cacheStats;
    private List<string> renderLog = new List<string>();
    private int maxLogEntries = 100;
    
    [MenuItem("Tools/Inventory/Render Debugger")]
    public static void ShowWindow()
    {
        GetWindow<InventoryRenderDebugger>("Inventory Render Debugger");
    }
    
    void OnEnable()
    {
        lastRefreshTime = EditorApplication.timeSinceStartup;
        EditorApplication.update += OnEditorUpdate;
    }
    
    void OnDisable()
    {
        EditorApplication.update -= OnEditorUpdate;
    }
    
    void OnEditorUpdate()
    {
        if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
        {
            RefreshData();
            lastRefreshTime = EditorApplication.timeSinceStartup;
            Repaint();
        }
    }
    
    void OnGUI()
    {
        EditorGUILayout.BeginVertical();
        
        // Tab selection
        selectedTab = GUILayout.Toolbar(selectedTab, tabNames);
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        switch (selectedTab)
        {
            case 0:
                DrawMonitorTab();
                break;
            case 1:
                DrawTestRenderTab();
                break;
            case 2:
                DrawCacheTab();
                break;
            case 3:
                DrawSettingsTab();
                break;
        }
        
        EditorGUILayout.EndScrollView();
        EditorGUILayout.EndVertical();
    }
    
    void DrawMonitorTab()
    {
        EditorGUILayout.LabelField("Render System Monitor", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // System status
        bool isSystemActive = InventoryModelRenderer.Instance != null;
        EditorGUILayout.LabelField("System Status", isSystemActive ? "Active" : "Inactive");
        
        if (!isSystemActive)
        {
            EditorGUILayout.HelpBox("Inventory Model Renderer is not active. Enter play mode to initialize.", MessageType.Warning);
            return;
        }
        
        var renderer = InventoryModelRenderer.Instance;
        
        // Queue information
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Queue Status", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Total Queue Size", renderer.GetQueueSize().ToString());
        EditorGUILayout.LabelField("Priority Queue", renderer.GetPriorityQueueSize().ToString());
        EditorGUILayout.LabelField("Normal Queue", renderer.GetNormalQueueSize().ToString());
        EditorGUILayout.LabelField("Is Rendering", renderer.IsRendering().ToString());
        
        // Cache statistics
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Cache Statistics", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Cached Items", cacheStats.entryCount.ToString());
        EditorGUILayout.LabelField("Memory Usage", $"{cacheStats.memoryUsage / (1024 * 1024):F1} MB");
        EditorGUILayout.LabelField("Max Memory", $"{cacheStats.maxMemoryUsage / (1024 * 1024):F1} MB");
        EditorGUILayout.LabelField("Hit Rate", $"{cacheStats.hitRate:P1}");
        
        // Memory usage bar
        float memoryPercent = cacheStats.maxMemoryUsage > 0 ? (float)cacheStats.memoryUsage / cacheStats.maxMemoryUsage : 0f;
        Rect memoryRect = EditorGUILayout.GetControlRect();
        EditorGUI.ProgressBar(memoryRect, memoryPercent, $"Memory: {memoryPercent:P1}");
        
        // Render log
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Recent Activity", EditorStyles.boldLabel);
        
        if (renderLog.Count == 0)
        {
            EditorGUILayout.LabelField("No recent activity");
        }
        else
        {
            for (int i = renderLog.Count - 1; i >= Mathf.Max(0, renderLog.Count - 10); i--)
            {
                EditorGUILayout.LabelField(renderLog[i], EditorStyles.miniLabel);
            }
        }
        
        // Control buttons
        EditorGUILayout.Space();
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Clear Cache"))
        {
            renderer.ClearCache();
            AddToLog("Cache cleared manually");
        }
        if (GUILayout.Button("Refresh"))
        {
            RefreshData();
        }
        EditorGUILayout.EndHorizontal();
    }
    
    void DrawTestRenderTab()
    {
        EditorGUILayout.LabelField("Test Rendering", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Item selection
        testItem = (ItemSO)EditorGUILayout.ObjectField("Test Item", testItem, typeof(ItemSO), false);
        
        if (testItem == null)
        {
            EditorGUILayout.HelpBox("Select an ItemSO to test rendering", MessageType.Info);
            return;
        }
        
        EditorGUILayout.Space();
        
        // Render buttons
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Render Normal Priority"))
        {
            TestRender(InventoryModelRenderer.RenderPriority.Normal);
        }
        if (GUILayout.Button("Render High Priority"))
        {
            TestRender(InventoryModelRenderer.RenderPriority.High);
        }
        EditorGUILayout.EndHorizontal();
        
        if (GUILayout.Button("Clear Test Texture"))
        {
            previewTexture = null;
        }
        
        // Preview
        if (previewTexture != null)
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Preview", EditorStyles.boldLabel);
            
            float previewSize = 200f;
            Rect previewRect = EditorGUILayout.GetControlRect(false, previewSize);
            previewRect.width = previewSize;
            
            EditorGUI.DrawPreviewTexture(previewRect, previewTexture);
            
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"Texture Size: {previewTexture.width}x{previewTexture.height}");
            EditorGUILayout.LabelField($"Format: {previewTexture.format}");
        }
    }
    
    void DrawCacheTab()
    {
        EditorGUILayout.LabelField("Cache Management", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        if (InventoryModelRenderer.Instance == null)
        {
            EditorGUILayout.HelpBox("Renderer not active", MessageType.Warning);
            return;
        }
        
        var renderer = InventoryModelRenderer.Instance;
        
        // Cache controls
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Clear All Cache"))
        {
            renderer.ClearCache();
            AddToLog("All cache cleared");
        }
        if (GUILayout.Button("Refresh Stats"))
        {
            RefreshData();
        }
        EditorGUILayout.EndHorizontal();
        
        EditorGUILayout.Space();
        
        // Cache statistics detailed view
        EditorGUILayout.LabelField("Detailed Cache Information", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Total Entries: {cacheStats.entryCount}");
        EditorGUILayout.LabelField($"Memory Usage: {cacheStats.memoryUsage:N0} bytes");
        EditorGUILayout.LabelField($"Max Memory: {cacheStats.maxMemoryUsage:N0} bytes");
        EditorGUILayout.LabelField($"Hit Rate: {cacheStats.hitRate:P2}");
        
        // Memory usage visualization
        EditorGUILayout.Space();
        float memoryPercent = cacheStats.maxMemoryUsage > 0 ? (float)cacheStats.memoryUsage / cacheStats.maxMemoryUsage : 0f;
        
        EditorGUILayout.LabelField("Memory Usage");
        Rect memoryRect = EditorGUILayout.GetControlRect();
        EditorGUI.ProgressBar(memoryRect, memoryPercent, $"{memoryPercent:P1} ({cacheStats.memoryUsage / (1024 * 1024):F1} MB)");
        
        // Cache efficiency indicators
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Cache Efficiency", EditorStyles.boldLabel);
        
        Color originalColor = GUI.color;
        
        if (cacheStats.hitRate > 0.8f)
        {
            GUI.color = Color.green;
            EditorGUILayout.LabelField("✓ Excellent hit rate", EditorStyles.miniLabel);
        }
        else if (cacheStats.hitRate > 0.6f)
        {
            GUI.color = Color.yellow;
            EditorGUILayout.LabelField("⚠ Good hit rate", EditorStyles.miniLabel);
        }
        else
        {
            GUI.color = Color.red;
            EditorGUILayout.LabelField("✗ Poor hit rate", EditorStyles.miniLabel);
        }
        
        GUI.color = originalColor;
    }
    
    void DrawSettingsTab()
    {
        EditorGUILayout.LabelField("Debug Settings", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Auto refresh settings
        autoRefresh = EditorGUILayout.Toggle("Auto Refresh", autoRefresh);
        if (autoRefresh)
        {
            refreshInterval = EditorGUILayout.Slider("Refresh Interval", refreshInterval, 0.1f, 5f);
        }
        
        EditorGUILayout.Space();
        
        // Log settings
        EditorGUILayout.LabelField("Logging", EditorStyles.boldLabel);
        maxLogEntries = EditorGUILayout.IntSlider("Max Log Entries", maxLogEntries, 10, 500);
        
        if (GUILayout.Button("Clear Log"))
        {
            renderLog.Clear();
        }
        
        EditorGUILayout.Space();
        
        // System information
        EditorGUILayout.LabelField("System Information", EditorStyles.boldLabel);
        EditorGUILayout.LabelField($"Unity Version: {Application.unityVersion}");
        EditorGUILayout.LabelField($"Platform: {Application.platform}");
        EditorGUILayout.LabelField($"Graphics Device: {SystemInfo.graphicsDeviceName}");
        EditorGUILayout.LabelField($"Graphics Memory: {SystemInfo.graphicsMemorySize} MB");
    }
    
    void TestRender(InventoryModelRenderer.RenderPriority priority)
    {
        if (testItem == null || InventoryModelRenderer.Instance == null)
            return;
            
        AddToLog($"Testing render for {testItem.name} (Priority: {priority})");
        
        InventoryModelRenderer.Instance.RequestRender(
            testItem,
            (texture) =>
            {
                previewTexture = texture;
                AddToLog($"Render completed for {testItem.name}");
                Repaint();
            },
            useCache: false, // Don't use cache for testing
            priority: priority
        );
    }
    
    void RefreshData()
    {
        if (InventoryModelRenderer.Instance != null)
        {
            cacheStats = InventoryModelRenderer.Instance.GetCacheStats();
        }
    }
    
    void AddToLog(string message)
    {
        string timestamp = System.DateTime.Now.ToString("HH:mm:ss");
        renderLog.Add($"[{timestamp}] {message}");
        
        // Limit log size
        while (renderLog.Count > maxLogEntries)
        {
            renderLog.RemoveAt(0);
        }
    }
}

#endif
