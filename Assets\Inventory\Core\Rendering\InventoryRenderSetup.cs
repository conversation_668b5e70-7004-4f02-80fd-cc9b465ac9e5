using UnityEngine;

/// <summary>
/// Helper component for easy setup and configuration of the inventory rendering system.
/// Can be added to any GameObject to initialize the rendering system with custom settings.
/// </summary>
public class InventoryRenderSetup : MonoBehaviour
{
    [Header("Render Area Configuration")]
    [SerializeField] private Vector3 customRenderPosition = Vector3.zero;
    [SerializeField] private bool useCustomPosition = false;
    [SerializeField] private bool findOptimalPosition = true;
    
    [Header("Render Settings")]
    [SerializeField] private int renderTextureSize = 256;
    [SerializeField] private RenderTextureFormat textureFormat = RenderTextureFormat.ARGB32;
    [SerializeField] private Color backgroundColor = Color.clear;
    
    [Header("Camera Settings")]
    [SerializeField] private float fieldOfView = 60f;
    [SerializeField] private float cameraThreshold = 0.1f;
    
    [Header("Lighting Configuration")]
    [SerializeField] private Color lightColor = Color.white;
    [SerializeField] private float lightIntensity = 1.2f;
    [SerializeField] private Vector3 lightRotation = new Vector3(50f, -30f, 0f);
    [SerializeField] private bool enableFillLighting = true;
    
    [Header("Cache Settings")]
    [SerializeField] private int maxCacheSize = 100;
    [SerializeField] private float cacheExpirationTime = 300f;
    [SerializeField] private bool enablePersistentCache = false;
    
    [Header("Performance")]
    [SerializeField] private int maxRendersPerFrame = 3;
    [SerializeField] private long maxMemoryUsage = 100 * 1024 * 1024; // 100MB
    
    [Header("Testing")]
    [SerializeField] private ItemSO[] testItems;
    [SerializeField] private bool preloadTestItems = false;

    // Public properties for editor access
    public bool UseCustomPosition => useCustomPosition;
    public Vector3 CustomRenderPosition => customRenderPosition;
    
    void Start()
    {
        InitializeRenderSystem();
    }
    
    /// <summary>
    /// Initializes the render system with the configured settings.
    /// </summary>
    public void InitializeRenderSystem()
    {
        // Get or create the renderer instance
        var renderer = InventoryModelRenderer.Instance;
        
        if (renderer == null)
        {
            Debug.LogError("Failed to initialize InventoryModelRenderer");
            return;
        }
        
        // Apply custom settings
        ApplySettings(renderer);
        
        // Preload test items if enabled
        if (preloadTestItems && testItems != null && testItems.Length > 0)
        {
            PreloadItems();
        }
        
        Debug.Log("Inventory render system initialized with custom settings");
    }
    
    /// <summary>
    /// Applies the configured settings to the renderer.
    /// </summary>
    private void ApplySettings(InventoryModelRenderer renderer)
    {
        // Update render position if custom position is specified
        if (useCustomPosition)
        {
            renderer.SetRenderAreaPosition(customRenderPosition);
        }
        else if (findOptimalPosition)
        {
            Vector3 optimalPosition = InventoryRenderAreaSetup.FindOptimalRenderPosition();
            renderer.SetRenderAreaPosition(optimalPosition);
        }
        
        // Update render texture settings
        renderer.UpdateRenderSettings(renderTextureSize, textureFormat);
        
        // Note: Other settings like lighting, camera, and cache settings
        // would need to be exposed through the renderer's public API
        // or applied during the renderer's initialization
    }
    
    /// <summary>
    /// Preloads the test items for immediate availability.
    /// </summary>
    private void PreloadItems()
    {
        var renderer = InventoryModelRenderer.Instance;
        if (renderer != null && testItems != null)
        {
            renderer.PreloadItems(testItems, () =>
            {
                Debug.Log($"Preloaded {testItems.Length} test items");
            });
        }
    }
    
    /// <summary>
    /// Tests rendering of a specific item.
    /// </summary>
    /// <param name="item">Item to test render</param>
    /// <param name="priority">Render priority</param>
    public void TestRenderItem(ItemSO item, InventoryModelRenderer.RenderPriority priority = InventoryModelRenderer.RenderPriority.Normal)
    {
        if (item == null)
        {
            Debug.LogWarning("Cannot test render null item");
            return;
        }
        
        var renderer = InventoryModelRenderer.Instance;
        if (renderer != null)
        {
            renderer.RequestRender(item, (texture) =>
            {
                if (texture != null)
                {
                    Debug.Log($"Successfully rendered {item.name} - Texture: {texture.width}x{texture.height}");
                }
                else
                {
                    Debug.LogError($"Failed to render {item.name}");
                }
            }, useCache: false, priority: priority);
        }
    }
    
    /// <summary>
    /// Tests rendering of all configured test items.
    /// </summary>
    [ContextMenu("Test Render All Items")]
    public void TestRenderAllItems()
    {
        if (testItems == null || testItems.Length == 0)
        {
            Debug.LogWarning("No test items configured");
            return;
        }
        
        Debug.Log($"Testing render for {testItems.Length} items");
        
        foreach (var item in testItems)
        {
            if (item != null)
            {
                TestRenderItem(item);
            }
        }
    }
    
    /// <summary>
    /// Clears the render cache.
    /// </summary>
    [ContextMenu("Clear Cache")]
    public void ClearCache()
    {
        var renderer = InventoryModelRenderer.Instance;
        if (renderer != null)
        {
            renderer.ClearCache();
            Debug.Log("Render cache cleared");
        }
    }
    
    /// <summary>
    /// Prints current cache statistics.
    /// </summary>
    [ContextMenu("Print Cache Stats")]
    public void PrintCacheStats()
    {
        var renderer = InventoryModelRenderer.Instance;
        if (renderer != null)
        {
            var stats = renderer.GetCacheStats();
            Debug.Log($"Cache Stats - Entries: {stats.entryCount}, Memory: {stats.memoryUsage / (1024 * 1024):F1}MB, Hit Rate: {stats.hitRate:P1}");
        }
    }
    
    /// <summary>
    /// Validates the current render area isolation.
    /// </summary>
    [ContextMenu("Validate Isolation")]
    public void ValidateIsolation()
    {
        var renderer = InventoryModelRenderer.Instance;
        if (renderer == null)
        {
            Debug.LogWarning("Renderer not initialized");
            return;
        }
        
        // Create a rough estimate of game world bounds
        // In a real implementation, you might want to calculate this more precisely
        Bounds gameWorldBounds = new Bounds(Vector3.zero, Vector3.one * 1000f);
        
        Vector3 renderPosition = useCustomPosition ? customRenderPosition : 
                                InventoryRenderAreaSetup.FindOptimalRenderPosition();
        
        bool isIsolated = InventoryRenderAreaSetup.ValidateRenderAreaIsolation(renderPosition, gameWorldBounds);
        
        if (isIsolated)
        {
            Debug.Log($"✓ Render area is properly isolated at position {renderPosition}");
        }
        else
        {
            Debug.LogWarning($"⚠ Render area at {renderPosition} may not be sufficiently isolated from game world");
        }
    }
    
    void OnValidate()
    {
        // Clamp values to reasonable ranges
        renderTextureSize = Mathf.Clamp(renderTextureSize, 64, 2048);
        maxCacheSize = Mathf.Clamp(maxCacheSize, 10, 1000);
        cacheExpirationTime = Mathf.Clamp(cacheExpirationTime, 10f, 3600f);
        maxRendersPerFrame = Mathf.Clamp(maxRendersPerFrame, 1, 10);
        fieldOfView = Mathf.Clamp(fieldOfView, 10f, 120f);
        cameraThreshold = Mathf.Clamp(cameraThreshold, 0.01f, 1f);
        lightIntensity = Mathf.Clamp(lightIntensity, 0.1f, 5f);
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw the render area position in the scene view
        Vector3 renderPos = useCustomPosition ? customRenderPosition : 
                           InventoryRenderAreaSetup.FindOptimalRenderPosition();
        
        Gizmos.color = Color.cyan;
        Gizmos.DrawWireCube(renderPos, Vector3.one * 10f);
        Gizmos.DrawIcon(renderPos, "Camera Icon", true);
        
        // Draw connection line from this object to render area
        Gizmos.color = Color.yellow;
        Gizmos.DrawLine(transform.position, renderPos);
    }
}
