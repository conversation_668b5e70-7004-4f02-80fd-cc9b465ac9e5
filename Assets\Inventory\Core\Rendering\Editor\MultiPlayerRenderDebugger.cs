using UnityEngine;
using UnityEditor;
using System.Linq;

/// <summary>
/// Editor window for debugging and monitoring the multi-player inventory rendering system.
/// </summary>
public class MultiPlayerRenderDebugger : EditorWindow
{
    private Vector2 scrollPosition;
    private int selectedTab = 0;
    private readonly string[] tabNames = { "Overview", "Players", "Performance", "Configuration", "Testing" };
    
    private bool autoRefresh = true;
    private float refreshInterval = 1f;
    private double lastRefreshTime;
    
    [MenuItem("Tools/Inventory/Multi-Player Render Debugger")]
    public static void ShowWindow()
    {
        var window = GetWindow<MultiPlayerRenderDebugger>("MP Render Debug");
        window.minSize = new Vector2(600, 400);
        window.Show();
    }
    
    void OnEnable()
    {
        lastRefreshTime = EditorApplication.timeSinceStartup;
    }
    
    void OnGUI()
    {
        DrawHeader();
        DrawTabs();
        
        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
        
        switch (selectedTab)
        {
            case 0: DrawOverviewTab(); break;
            case 1: DrawPlayersTab(); break;
            case 2: DrawPerformanceTab(); break;
            case 3: DrawConfigurationTab(); break;
            case 4: DrawTestingTab(); break;
        }
        
        EditorGUILayout.EndScrollView();
        
        // Auto-refresh
        if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
        {
            lastRefreshTime = EditorApplication.timeSinceStartup;
            Repaint();
        }
    }
    
    private void DrawHeader()
    {
        EditorGUILayout.BeginHorizontal(EditorStyles.toolbar);
        
        GUILayout.Label("Multi-Player Inventory Renderer Debugger", EditorStyles.boldLabel);
        
        GUILayout.FlexibleSpace();
        
        autoRefresh = GUILayout.Toggle(autoRefresh, "Auto Refresh", EditorStyles.toolbarButton);
        
        if (GUILayout.Button("Refresh", EditorStyles.toolbarButton))
        {
            Repaint();
        }
        
        EditorGUILayout.EndHorizontal();
    }
    
    private void DrawTabs()
    {
        selectedTab = GUILayout.Toolbar(selectedTab, tabNames);
        EditorGUILayout.Space();
    }
    
    private void DrawOverviewTab()
    {
        EditorGUILayout.LabelField("System Overview", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        // Current mode
        bool isMultiPlayer = InventoryRendererAdapter.IsMultiPlayerMode;
        EditorGUILayout.LabelField("Current Mode:", isMultiPlayer ? "Multi-Player" : "Single-Player");
        
        // Active renderer
        var renderer = InventoryRendererAdapter.GetRenderer();
        string rendererType = renderer?.GetType().Name ?? "None";
        EditorGUILayout.LabelField("Active Renderer:", rendererType);
        
        EditorGUILayout.Space();
        
        // Player count
        int playerCount = 0;
        if (Application.isPlaying && PlayersManager.Instance != null)
        {
            playerCount = PlayersManager.Instance.ActiveUsers.Count;
        }
        EditorGUILayout.LabelField("Active Players:", playerCount.ToString());
        
        // Queue status
        int totalQueueSize = InventoryRendererAdapter.GetQueueSize();
        EditorGUILayout.LabelField("Total Queue Size:", totalQueueSize.ToString());
        
        bool isRendering = InventoryRendererAdapter.IsRendering();
        EditorGUILayout.LabelField("Currently Rendering:", isRendering ? "Yes" : "No");
        
        EditorGUILayout.Space();
        
        // Cache status
        if (renderer != null)
        {
            bool hasCache = InventoryRendererAdapter.HasCachedTexture(null); // This will check if cache exists
            EditorGUILayout.LabelField("Cache Available:", hasCache ? "Yes" : "No");
        }
        
        EditorGUILayout.Space();
        
        // Quick actions
        EditorGUILayout.LabelField("Quick Actions", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Clear Cache"))
        {
            InventoryRendererAdapter.ClearCache();
            Debug.Log("Inventory render cache cleared");
        }
        
        if (GUILayout.Button("Refresh Mode Detection"))
        {
            InventoryRendererAdapter.RefreshModeDetection();
            Debug.Log("Mode detection refreshed");
        }
        EditorGUILayout.EndHorizontal();
    }
    
    private void DrawPlayersTab()
    {
        EditorGUILayout.LabelField("Player Information", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Player information is only available during play mode.", MessageType.Info);
            return;
        }
        
        if (PlayersManager.Instance == null)
        {
            EditorGUILayout.HelpBox("PlayersManager not found.", MessageType.Warning);
            return;
        }
        
        var players = PlayersManager.Instance.ActiveUsers;
        if (players.Count == 0)
        {
            EditorGUILayout.HelpBox("No active players found.", MessageType.Info);
            return;
        }
        
        foreach (var player in players)
        {
            EditorGUILayout.BeginVertical(EditorStyles.helpBox);
            
            EditorGUILayout.LabelField($"Player {player.PlayerIndex + 1}", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("User ID:", player.UserID);
            EditorGUILayout.LabelField("State:", player.CurrentState.ToString());
            
            // Render area info
            if (InventoryRendererAdapter.IsMultiPlayerMode)
            {
                Vector3 renderPos = InventoryRenderingConfig.Instance.GetPlayerRenderPosition(player.PlayerIndex);
                EditorGUILayout.LabelField("Render Position:", renderPos.ToString("F1"));
                
                int queueSize = InventoryRendererAdapter.GetPlayerQueueSize(player.UserID);
                EditorGUILayout.LabelField("Queue Size:", queueSize.ToString());
                
                // Validation
                bool isValidPosition = InventoryRenderingConfig.Instance.ValidateRenderPosition(renderPos, player.PlayerIndex);
                EditorGUILayout.LabelField("Position Valid:", isValidPosition ? "Yes" : "No");
                if (!isValidPosition)
                {
                    EditorGUILayout.HelpBox("Render position may not be adequately isolated!", MessageType.Warning);
                }
            }
            
            EditorGUILayout.EndVertical();
            EditorGUILayout.Space();
        }
    }
    
    private void DrawPerformanceTab()
    {
        EditorGUILayout.LabelField("Performance Monitoring", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Performance data is only available during play mode.", MessageType.Info);
            return;
        }
        
        var config = InventoryRenderingConfig.Instance;
        
        // Configuration limits
        EditorGUILayout.LabelField("Configuration Limits", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Max Renders/Frame:", config.GetMaxRendersPerFrame().ToString());
        
        if (InventoryRendererAdapter.IsMultiPlayerMode)
        {
            EditorGUILayout.LabelField("Max Renders/Frame/Player:", config.MaxRendersPerFramePerPlayer.ToString());
        }
        
        EditorGUILayout.LabelField("Cache Size:", config.GetCacheSize().ToString());
        EditorGUILayout.LabelField("Cache Expiration:", $"{config.CacheExpirationTime}s");
        
        EditorGUILayout.Space();
        
        // Current performance
        EditorGUILayout.LabelField("Current Performance", EditorStyles.boldLabel);
        
        int totalQueue = InventoryRendererAdapter.GetQueueSize();
        EditorGUILayout.LabelField("Total Queue Size:", totalQueue.ToString());
        
        bool isRendering = InventoryRendererAdapter.IsRendering();
        EditorGUILayout.LabelField("Currently Rendering:", isRendering ? "Yes" : "No");
        
        // Performance warnings
        if (totalQueue > config.GetMaxRendersPerFrame() * 3)
        {
            EditorGUILayout.HelpBox("Queue size is getting large. Consider optimizing render requests.", MessageType.Warning);
        }
        
        EditorGUILayout.Space();
        
        // Render texture info
        EditorGUILayout.LabelField("Render Texture Settings", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Size:", $"{config.RenderTextureSize}x{config.RenderTextureSize}");
        EditorGUILayout.LabelField("Format:", config.RenderTextureFormat.ToString());
        EditorGUILayout.LabelField("Depth:", $"{config.RenderTextureDepth} bit");
        
        // Memory estimation
        float memoryPerTexture = (config.RenderTextureSize * config.RenderTextureSize * 4) / (1024f * 1024f); // Rough RGBA estimate
        float totalMemory = memoryPerTexture * config.GetCacheSize();
        EditorGUILayout.LabelField("Est. Cache Memory:", $"{totalMemory:F1} MB");
    }
    
    private void DrawConfigurationTab()
    {
        EditorGUILayout.LabelField("Configuration", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        var config = InventoryRenderingConfig.Instance;
        
        EditorGUILayout.LabelField("Mode Detection", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Force Multi-Player:", config.ForceMultiPlayerMode.ToString());
        EditorGUILayout.LabelField("Auto Detect:", config.AutoDetectMode.ToString());
        EditorGUILayout.LabelField("Multi-Player Threshold:", config.MultiPlayerThreshold.ToString());
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("Multi-Player Settings", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Max Supported Players:", config.MaxSupportedPlayers.ToString());
        EditorGUILayout.LabelField("Player Area Separation:", $"{config.PlayerAreaSeparation:F0} units");
        EditorGUILayout.LabelField("Base Render Position:", config.BaseRenderPosition.ToString("F0"));
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("Isolation Validation", EditorStyles.boldLabel);
        EditorGUILayout.LabelField("Validate Isolation:", config.ValidateIsolation.ToString());
        EditorGUILayout.LabelField("Min Isolation Distance:", $"{config.MinimumIsolationDistance:F0} units");
        EditorGUILayout.LabelField("Log Warnings:", config.LogIsolationWarnings.ToString());
        
        EditorGUILayout.Space();
        
        if (GUILayout.Button("Log Full Configuration"))
        {
            config.LogConfiguration();
        }
        
        if (GUILayout.Button("Open Configuration Asset"))
        {
            Selection.activeObject = config;
            EditorGUIUtility.PingObject(config);
        }
    }
    
    private void DrawTestingTab()
    {
        EditorGUILayout.LabelField("Testing Tools", EditorStyles.boldLabel);
        EditorGUILayout.Space();
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("Testing tools are only available during play mode.", MessageType.Info);
            return;
        }
        
        EditorGUILayout.LabelField("Isolation Testing", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Test All Player Positions"))
        {
            TestAllPlayerPositions();
        }
        
        if (GUILayout.Button("Validate Current Configuration"))
        {
            ValidateCurrentConfiguration();
        }
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("Performance Testing", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Simulate Heavy Load"))
        {
            SimulateHeavyLoad();
        }
        
        EditorGUILayout.Space();
        
        EditorGUILayout.LabelField("Cache Testing", EditorStyles.boldLabel);
        
        if (GUILayout.Button("Test Cache Performance"))
        {
            TestCachePerformance();
        }
    }
    
    private void TestAllPlayerPositions()
    {
        var config = InventoryRenderingConfig.Instance;
        Debug.Log("Testing player render positions...");
        
        for (int i = 0; i < config.MaxSupportedPlayers; i++)
        {
            Vector3 position = config.GetPlayerRenderPosition(i);
            bool isValid = config.ValidateRenderPosition(position, i);
            
            Debug.Log($"Player {i}: Position {position}, Valid: {isValid}");
        }
    }
    
    private void ValidateCurrentConfiguration()
    {
        var config = InventoryRenderingConfig.Instance;
        Debug.Log("Validating current configuration...");
        
        // Test mode detection
        bool shouldUseMultiPlayer = config.ShouldUseMultiPlayerRendering();
        bool actuallyUsingMultiPlayer = InventoryRendererAdapter.IsMultiPlayerMode;
        
        Debug.Log($"Should use multi-player: {shouldUseMultiPlayer}, Actually using: {actuallyUsingMultiPlayer}");
        
        if (shouldUseMultiPlayer != actuallyUsingMultiPlayer)
        {
            Debug.LogWarning("Mode detection mismatch detected!");
        }
    }
    
    private void SimulateHeavyLoad()
    {
        Debug.Log("Simulating heavy rendering load...");
        // This would simulate multiple render requests
        // Implementation would depend on having test ItemSO assets
    }
    
    private void TestCachePerformance()
    {
        Debug.Log("Testing cache performance...");
        // This would test cache hit/miss rates and performance
        // Implementation would depend on having test ItemSO assets
    }
}
