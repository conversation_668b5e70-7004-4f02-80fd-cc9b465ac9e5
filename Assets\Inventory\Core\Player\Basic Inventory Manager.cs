using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Unity.VisualScripting;

public class BasicInventoryManager : MonoBehaviour
{
    public float DistanceToCamera = 0.5f;

    [Header("Inventory Logic")]                 
    public Inventory inventory;                                                                                        
    public IInventorySlot equippedSlot;

    [<PERSON><PERSON>("Inventory UI Logic")]
    [Tooltip("The inventory UI component to manage.")]
    [SerializeField] private InventoryUI targetInventoryUI;
    public CanvasGroup canvasGroup;
    private Vector3 originalBackpackPosition;
    private bool isOpened = false;

    [Tooltip("The slot that is currently being hovered. Used for drag and drop.")]
    public InventorySlotUI hoveredSlot;
    public InventorySlotUI selectedSlot;

    [Header("Item Preview")]
    public ItemSO previewItem;
    public GameObject previewModel;
    public RectTransform modelParent;

    [Header("Components")]

    [Tooltip("The player this inventory UI is associated with.")]
    public PlayerMonoBehaviour player;

    [Toolt<PERSON>("The camera this inventory UI is associated with.")]
    public Camera camera;

    [Tooltip("The player input handler this inventory UI is associated with.")]
    public PlayerInputHandler inputHandler;

    public List<InventorySlotUI> inventorySlots = new List<InventorySlotUI>();

    void Start()
    {
        targetInventoryUI.CloseInventory();
        if (inputHandler == null && player != null) inputHandler = player.GetInputHandler();
        if (inputHandler != null)
        {
            inputHandler.RegisterCallback(ToggleInventory, "Inventory");
            inputHandler.RegisterCallback(ToggleInventory, "Tab");
        }

        foreach (var slotUI in targetInventoryUI.slotUIs)
        {
            inventorySlots.Add(slotUI as InventorySlotUI);
        }

        targetInventoryUI.OnUpdateUI += UpdateSlots;
        camera = player.GetCamera();
    }

    public void UpdateSlots()
    {
        Debug.Log("Updating slots");
        inventorySlots.Clear();
        foreach (var slotUI in targetInventoryUI.slotUIs)
        {
            inventorySlots.Add(slotUI as InventorySlotUI);

            slotUI.OnSlotHovered -= OnSlotHovered;
            slotUI.OnSlotPressed -= OnSlotPressed;

            slotUI.OnSlotHovered += OnSlotHovered;
            slotUI.OnSlotPressed += OnSlotPressed;
        }

        canvasGroup.alpha = 0;
        targetInventoryUI.inventoryPanel.SetActive(true);
        targetInventoryUI.inventoryPanel.SetActive(false);
        canvasGroup.alpha = 1;
    }

    public void EquipItem()
    {
        int selectedIndex = inventorySlots.IndexOf(selectedSlot);
        IInventorySlot targetSlot = inventory.GetSlot(selectedIndex);
        if (targetSlot != null && targetSlot == equippedSlot)
        {
            UnequipItem();
            return;
        }
        else if (targetSlot != null)
        {
            equippedSlot = targetSlot;
            return;
        }
        equippedSlot = null;
    }

    public void DropItem()
    {
        int selectedIndex = inventorySlots.IndexOf(selectedSlot);
        IInventorySlot targetSlot = inventory.GetSlot(selectedIndex);
        if (targetSlot != null)
        {
            inventory.DropItem(targetSlot);
        }
    }

    public void UnequipItem()
    {
        equippedSlot = null;
    }

    #region UI Section

    public void OnSlotHovered(IInventorySlotUI slotUI, bool isHovered)
    {
        if (isHovered) hoveredSlot = slotUI as InventorySlotUI;
        else hoveredSlot = null;
    }

    public void OnSlotPressed(IInventorySlotUI slotUI)
    {
        ItemSO item = null;
        if(slotUI is InventorySlotUI inventorySlotUI)
        {
            if(selectedSlot == inventorySlotUI) {
                selectedSlot = null;
            }
            else{
                selectedSlot = inventorySlotUI;
            }

            IInventorySlot targetSlot = GetInventorySlot(inventorySlotUI);
            if (targetSlot != null)
            {
                item = targetSlot.Item();
            }
            PreviewItem(item);
        }
    }

    public void PreviewItem(ItemSO item)
    {
        if(item == null || previewItem == item) 
        {
            previewItem = null;
            if(previewModel != null) Destroy(previewModel); 
            return;
        }
        
        if(previewModel != null) Destroy(previewModel);
        
        previewItem = item;
        if(previewItem != null)
        {
            previewModel = Instantiate(previewItem.prefab, modelParent);
            previewModel.GetComponent<Collider>().isTrigger = true;
            previewModel.GetComponent<Rigidbody>().isKinematic = true;
            RectTransformUtils.FitGameObject(modelParent, previewModel, previewItem.modelInfo);
        }
    }

    #endregion

    void OnDisable()
    {
        if (inputHandler == null && player != null) inputHandler = player.GetInputHandler();
        if (inputHandler != null)
        {
            inputHandler.UnregisterCallback(ToggleInventory, "Inventory");
            inputHandler.UnregisterCallback(ToggleInventory, "Tab");
        }
        hoveredSlot = null;
        selectedSlot = null;
    }

    public void ToggleInventory(InputAction.CallbackContext context)
    {
        if (!context.started) return;

        var userUIManager = player.GetUser().GetUIManager();

        if (targetInventoryUI.inventoryPanel.activeSelf)
        {
            isOpened = false;
            userUIManager.CloseUIPanel(targetInventoryUI.inventoryPanel);
            targetInventoryUI.CloseInventory();
        }
        else
        {
            isOpened = true;
            userUIManager.ToggleExistingUIPanel(targetInventoryUI.inventoryPanel);
            targetInventoryUI.OpenInventory();

            Debug.Log("Camera position before: " + camera.transform.position);

            Debug.Log("Camera position after: " + camera.transform.position);
        }
    }

    public IInventorySlot GetInventorySlot(InventorySlotUI slotUI)
    {
        int slotIndex = targetInventoryUI.slotUIs.IndexOf(slotUI);
        IInventorySlot targetSlot = inventory.GetSlot(slotIndex);
        if (targetSlot != null)
        {
            return targetSlot;
        }
        return null;
    }

    public void ClearUI()
    {
        hoveredSlot = null;
        selectedSlot = null;
        previewItem = null;
        if(previewModel != null) Destroy(previewModel);
        previewModel = null;
    }
}