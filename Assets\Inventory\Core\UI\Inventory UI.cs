using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

public class InventoryUI : MonoBehaviour, IInventoryUI
{
    [Header("Inventory To Display")]
    [Tooltip("The inventory component whose contents will be displayed.")]
    [SerializeField] private Inventory targetInventory;

    [<PERSON><PERSON>("UI Prefabs & Parents")]
    [Toolt<PERSON>("The prefab for a single inventory slot UI element.")]
    [SerializeField] private GameObject slotUIPrefab;

    [Tooltip("The parent RectTransform where slot UI prefabs will be instantiated.")]
    [SerializeField] private RectTransform slotsParent;

    [Tooltip("The main panel that contains the inventory UI.")]
    public GameObject inventoryPanel;

    public List<IInventorySlotUI> slotUIs = new List<IInventorySlotUI>();
    public event System.Action OnUpdateUI;
    public event System.Action OnOpenInventory;
    public event System.Action OnCloseInventory;

    void OnEnable()
    {
        // Se inscreve no evento do inventário quando o objeto da UI é ativado
        if (targetInventory != null)
        {
            targetInventory.OnInventoryChanged += UpdateUI;
        }
    }

    void OnDisable()
    {
        // Cancela a inscrição do evento quando o objeto da UI é desativado
        if (targetInventory != null)
        {
            targetInventory.OnInventoryChanged -= UpdateUI;
        }
    }

    void Start()
    {
        if (targetInventory != null)
        {
            InitializeUI(targetInventory);
        }
    }
    
    /// <summary>
    /// Creates and initializes the UI slots based on the inventory's capacity.
    /// </summary>
    /// <param name="inventory">The inventory to represent.</param>
    public void InitializeUI(IInventory inventory)
    {
        // Limpa slots antigos se houver
        foreach (Transform child in slotsParent)
        {
            Destroy(child.gameObject);
        }
        slotUIs.Clear();

        // Cria um slot de UI para cada slot no inventário
        for (int i = 0; i < targetInventory.capacity; i++)
        {
            GameObject slotGO = Instantiate(slotUIPrefab, slotsParent);
            slotGO.name = "Slot " + i;
            IInventorySlotUI newSlotUI = slotGO.GetComponent<IInventorySlotUI>();
            if (newSlotUI != null)
            {
                Debug.Log("SlotUI criado 1");
                slotUIs.Add(newSlotUI);
                // No longer need to manually update model positions
                // The centralized renderer handles all rendering
            }
            else
            {
                Debug.LogError("InventorySlotUI prefab is missing a component that implements IInventorySlotUI.");
            }
        }
        
        // -----------------------------------------------------------------
        
        UpdateUI(); // Chama uma primeira vez para garantir que a UI esteja sincronizada
    }

    public void ToggleInventoryUI()
    {
        if(inventoryPanel.activeSelf) CloseInventory();
        else OpenInventory();
    }

    public void OpenInventory()
    {
        inventoryPanel.SetActive(true);
        OnOpenInventory?.Invoke();
    }

    public void CloseInventory()
    {
        inventoryPanel.SetActive(false);
        OnCloseInventory?.Invoke();
    }

    /// <summary>
    /// Updates all UI slots to reflect the current state of the inventory.
    /// This method is called by the OnInventoryChanged event.
    /// </summary>
    public void UpdateUI()
    {
        Debug.Log("Updating UI");
        if (slotUIs.Count != targetInventory.slots.Count)
        {
            // Se a capacidade do inventário mudou, reinicializa a UI
            InitializeUI(targetInventory);
            return;
        }

        for (int i = 0; i < slotUIs.Count; i++)
        {
            slotUIs[i].UpdateSlot(targetInventory.GetSlot(i));
        }
        OnUpdateUI?.Invoke();
    }

    public List<IInventorySlotUI> GetSlotUIs() { return slotUIs; }
    public IInventorySlotUI GetSlotUI(int index = -1) { return slotUIs[index]; }
    public IInventory GetInventory() { return targetInventory; }
}