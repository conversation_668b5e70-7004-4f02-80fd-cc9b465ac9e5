%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5db3e374ca939948ad2cf8aad56acdc, type: 3}
  m_Name: PaperTowels SO
  m_EditorClassIdentifier: 
  ID: 0
  itemName: Paper Towels
  description: muito papel
  icon: {fileID: 0}
  modelInfo:
    prefab: {fileID: 7947793988225324362, guid: c6035c07de09096479888957ad2b1db5, type: 3}
    defaultRotation: {x: -90, y: 90, z: 0}
    intrinsicForward: {x: 0.15, y: 0, z: 0}
    centerOffset: {x: -0.08583498, y: 0.3136091, z: 0}
    scaleRatio: {x: 1, y: 0.95491076, z: 1}
    meshBoundsRadius: 0.47641715
    useAutoBounds: 1
  stack: 1
  maxStack: 10
  value: 0
  weight: 0
  gridWidth: 3
  gridHeight: 1
  canBeRotated: 0
