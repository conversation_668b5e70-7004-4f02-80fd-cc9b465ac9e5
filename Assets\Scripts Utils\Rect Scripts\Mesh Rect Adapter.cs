using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using System.Linq;

[ExecuteInEditMode]
public class MeshRectAdapter : MonoBehaviour
{
    [Header("Model Configuration")]
    public ItemSO itemReference;
    public ModelInfo modelInfo;
    public GameObject targetModel;

    [Header("Scaling Adjustments")]
    [Range(0f, 1f)] // Garante que o valor esteja entre 0 e 1, onde 0.9 seria 90% do tamanho
    public float scaleThreshold = 1.0f; // Novo campo para o threshold. 1.0f significa sem threshold.

    private RectTransform _rectTransform;
    private Vector2 _lastRectSize;

    public Vector3 _calculatedMeshCenterLocal;
    public float _calculatedMeshRadius;

    private void Awake()
    {
        if (_rectTransform == null) _rectTransform = GetComponent<RectTransform>();
        targetModel = GetComponent<GameObject>();
        _rectTransform = GetComponent<RectTransform>();
        modelInfo = null;
        _lastRectSize = Vector2.zero;
    }

    private void Update()
    {
#if UNITY_EDITOR
        if (!Application.isPlaying)
            SafeUpdate();
#endif
    }

    private void SafeUpdate()
    {
        if (_rectTransform == null) return;
        if (_rectTransform.rect.size != _lastRectSize)
            UpdateModelScale();
    }

    public void UpdateModelScale()
    {
        if (itemReference != null) modelInfo = itemReference.modelInfo;
        if (targetModel == null || modelInfo == null || _rectTransform == null)
            return;

        Vector2 rectSize = _rectTransform.rect.size;
        Vector3 meshCenterLocal = Vector3.zero;
        float meshRadius = 0f;

        // Escolhe bounds e calcula o centro da malha
        if (modelInfo.useAutoBounds)
        {
            // Chame a nova função que retorna o centro e o raio corretamente
            meshCenterLocal = CalculateLocalMeshBoundsAndRadius(targetModel, out meshRadius);
            _calculatedMeshCenterLocal = meshCenterLocal;
            _calculatedMeshRadius = meshRadius;
        }
        else
        {
            meshRadius = modelInfo.meshBoundsRadius;
            meshCenterLocal = modelInfo.centerOffset;
            _calculatedMeshCenterLocal = modelInfo.centerOffset;
            _calculatedMeshRadius = modelInfo.meshBoundsRadius;
        }

        Debug.Log($"MeshRectAdapter: Updating scale with meshRadius {meshRadius} for rectSize {rectSize}");

        // Protege contra zero
        const float kMin = 0.001f;
        meshRadius = Mathf.Max(meshRadius, kMin);

        // Reduz o tamanho do rectSize disponível para o cálculo da escala pelo threshold
        Vector2 adjustedRectSize = rectSize * scaleThreshold;

        // Calcula uniformScale usando o diâmetro (2 * raio)
        float diameter = 2 * meshRadius;
        float scaleX = adjustedRectSize.x / (diameter * modelInfo.scaleRatio.x);
        float scaleY = adjustedRectSize.y / (diameter * modelInfo.scaleRatio.y);
        float uniform = Mathf.Min(scaleX, scaleY);

        // Limita a um teto razoável (opcional)
        uniform = Mathf.Clamp(uniform, 0.01f, 1000f);

        targetModel.transform.localScale = new Vector3(
            uniform * modelInfo.scaleRatio.x,
            uniform * modelInfo.scaleRatio.y,
            uniform * modelInfo.scaleRatio.z
        );

        targetModel.transform.localEulerAngles = modelInfo.defaultRotation;

        // Aplicar a translação para centralizar
        // O offset agora é o meshCenterLocal, que é o centro da AABB do modelo
        // Precisamos mover o modelo de volta por essa quantidade, mas no espaço *local* do targetModel
        // E considerando a escala e rotação já aplicadas.

        // Calculate the world offset of the model's current center
        // after scaling and rotation.
        // The model's local center, when scaled and rotated, becomes an offset in its parent's space.
        // We want to move the model so its *original* local center (meshCenterLocal)
        // aligns with its parent's local origin (0,0,0) after the transformations.

        // A posição final deve ser o oposto do centro local da malha, após ser escalada
        // e rotacionada pelo próprio modelo.
        Vector3 desiredLocalPosition = -meshCenterLocal;

        // Aplica a escala aos eixos do offset ANTES de rotacionar.
        // Isso garante que o offset seja correto para o modelo escalado.
        Vector3 scaledOffset = new Vector3(
            desiredLocalPosition.x * targetModel.transform.localScale.x,
            desiredLocalPosition.y * targetModel.transform.localScale.y,
            desiredLocalPosition.z * targetModel.transform.localScale.z
        );

        // Agora, aplique a rotação DO PRÓPRIO targetModel ao offset escalado.
        // Isso garante que o movimento para centralizar leve em conta a orientação final do modelo.
        targetModel.transform.localPosition = targetModel.transform.localRotation * scaledOffset;

        _lastRectSize = rectSize;
    }


    /// <summary>
    /// Calcula o raio da esfera circunscrita para todos os vértices
    /// </summary>
    private Vector3 CalculateLocalMeshSphereRadiusWithCenter(GameObject go)
    {
        // Get all MeshFilter components in the GameObject and its children
        MeshFilter[] filters = go.GetComponentsInChildren<MeshFilter>();

        // Also check for SkinnedMeshRenderer components for animated meshes
        SkinnedMeshRenderer[] skinnedRenderers = go.GetComponentsInChildren<SkinnedMeshRenderer>();

        if (filters.Length == 0 && skinnedRenderers.Length == 0)
        {
            Debug.LogWarning($"MeshRectAdapter: No mesh components found on {go.name}, using fallback bounds");
            return Vector3.positiveInfinity;
        }

        Vector3 min = Vector3.positiveInfinity;
        Vector3 max = Vector3.negativeInfinity;

        // Coletar todos os pontos
        foreach (var filter in filters)
        {
            if (filter.sharedMesh == null || !filter.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = filter.sharedMesh.vertices;
            Transform meshTransform = filter.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        foreach (var skinnedRenderer in skinnedRenderers)
        {
            if (skinnedRenderer.sharedMesh == null || !skinnedRenderer.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = skinnedRenderer.sharedMesh.vertices;
            Transform meshTransform = skinnedRenderer.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        if (min == Vector3.positiveInfinity || max == Vector3.negativeInfinity)
        {
            return Vector3.positiveInfinity;
        }

        return (min + max) * 0.5f;
    }
    /// <summary>
    /// Calcula o centro local da malha e o raio da esfera circunscrita para todos os vértices.
    /// Retorna o centro local da AABB do modelo e, por parâmetro out, o raio.
    /// </summary>
    private Vector3 CalculateLocalMeshBoundsAndRadius(GameObject go, out float radius)
    {
        MeshFilter[] filters = go.GetComponentsInChildren<MeshFilter>();
        SkinnedMeshRenderer[] skinnedRenderers = go.GetComponentsInChildren<SkinnedMeshRenderer>();

        if (filters.Length == 0 && skinnedRenderers.Length == 0)
        {
            Debug.LogWarning($"MeshRectAdapter: No mesh components found on {go.name}, using fallback bounds");
            radius = 0f; // Ou um valor padrão razoável
            return Vector3.zero; // Ou um valor padrão
        }

        Vector3 min = Vector3.positiveInfinity;
        Vector3 max = Vector3.negativeInfinity;

        // Coletar todos os pontos
        foreach (var filter in filters)
        {
            if (filter.sharedMesh == null || !filter.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = filter.sharedMesh.vertices;
            Transform meshTransform = filter.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        foreach (var skinnedRenderer in skinnedRenderers)
        {
            if (skinnedRenderer.sharedMesh == null || !skinnedRenderer.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = skinnedRenderer.sharedMesh.vertices;
            Transform meshTransform = skinnedRenderer.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        if (min == Vector3.positiveInfinity || max == Vector3.negativeInfinity)
        {
            radius = 0f;
            return Vector3.zero;
        }

        // Calcular o centro da AABB
        Vector3 center = (min + max) * 0.5f;

        // Calcular o raio da esfera que envolve a AABB
        // A distância do centro a qualquer um dos cantos da AABB é o raio da esfera circunscrita
        Vector3 extents = (max - min) * 0.5f;
        radius = extents.magnitude; // A magnitude do vetor de extents é o raio da esfera

        return center;
    }

    // Esta função é chamada no editor para desenhar gizmos
    void OnDrawGizmos()
    {
        // Garante que temos um modelo alvo e que ele está ativo para desenhar gizmos.
        if (targetModel == null || !targetModel.activeInHierarchy)
        {
            return;
        }

        // A cor do gizmo para a esfera.
        Gizmos.color = Color.cyan;

        // 1. Calcula a posição global do centro da malha.
        // _calculatedMeshCenterLocal é o centro no espaço local do targetModel.
        // TransformPoint converte um ponto de coordenadas locais para coordenadas mundiais.
        Vector3 gizmoCenterGlobal = targetModel.transform.TransformPoint(_calculatedMeshCenterLocal);

        // 2. Calcula o raio escalado.
        // O raio da esfera deve ser escalado pela maior componente da escala global do targetModel.
        // Isso garante que a esfera circunscrita represente o modelo no seu tamanho atual.
        float maxScaleComponent = Mathf.Max(targetModel.transform.lossyScale.x,
                                            targetModel.transform.lossyScale.y,
                                            targetModel.transform.lossyScale.z);
        float scaledRadius = _calculatedMeshRadius * maxScaleComponent;

        // Desenha uma esfera de arame no centro global calculado com o raio escalado.
        Gizmos.DrawWireSphere(gizmoCenterGlobal, scaledRadius);

        // Opcional: Desenhar um pequeno cubo no centro da malha para clareza
        Gizmos.color = Color.red; // Cor diferente para o centro
        // O cubo também é desenhado no mesmo centro global.
        // O tamanho do cubo pode ser uma fração do raio escalado para ser visível.
        Gizmos.DrawWireCube(gizmoCenterGlobal, Vector3.one * (scaledRadius * 0.1f));
    }
}
