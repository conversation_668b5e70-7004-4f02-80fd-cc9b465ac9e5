using UnityEngine;

/// <summary>
/// Adapter that automatically chooses between single-player and multi-player rendering
/// based on the current game configuration. Provides a unified interface for inventory rendering.
/// </summary>
public static class InventoryRendererAdapter
{
    private static bool? _isMultiPlayerMode = null;
    
    /// <summary>
    /// Determines if the game is running in multi-player mode.
    /// </summary>
    public static bool IsMultiPlayerMode
    {
        get
        {
            if (_isMultiPlayerMode == null)
            {
                _isMultiPlayerMode = DetectMultiPlayerMode();
            }
            return _isMultiPlayerMode.Value;
        }
    }
    
    /// <summary>
    /// Gets the appropriate renderer instance based on the current mode.
    /// </summary>
    public static IInventoryRenderer GetRenderer()
    {
        if (IsMultiPlayerMode)
        {
            return MultiPlayerInventoryRenderer.Instance;
        }
        else
        {
            return InventoryModelRenderer.Instance;
        }
    }
    
    /// <summary>
    /// Requests rendering with automatic player detection.
    /// </summary>
    public static void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, 
                                   bool useCache = true, InventoryModelRenderer.RenderPriority priority = InventoryModelRenderer.RenderPriority.Normal)
    {
        var renderer = GetRenderer();
        
        if (IsMultiPlayerMode && renderer is MultiPlayerInventoryRenderer multiRenderer)
        {
            // Try to detect the current player context
            string playerID = GetCurrentPlayerContext();
            var multiPriority = priority == InventoryModelRenderer.RenderPriority.High ? 
                               MultiPlayerInventoryRenderer.RenderPriority.High : 
                               MultiPlayerInventoryRenderer.RenderPriority.Normal;
            
            multiRenderer.RequestRender(itemSO, onComplete, playerID, useCache, multiPriority);
        }
        else if (renderer is InventoryModelRenderer singleRenderer)
        {
            singleRenderer.RequestRender(itemSO, onComplete, useCache, priority);
        }
        else
        {
            Debug.LogError("No valid inventory renderer found");
            onComplete?.Invoke(null);
        }
    }
    
    /// <summary>
    /// Requests rendering for a specific player (multi-player mode only).
    /// </summary>
    public static void RequestRenderForPlayer(ItemSO itemSO, System.Action<RenderTexture> onComplete, 
                                            string playerID, bool useCache = true, 
                                            InventoryModelRenderer.RenderPriority priority = InventoryModelRenderer.RenderPriority.Normal)
    {
        if (!IsMultiPlayerMode)
        {
            Debug.LogWarning("RequestRenderForPlayer called in single-player mode. Using standard render.");
            RequestRender(itemSO, onComplete, useCache, priority);
            return;
        }
        
        var renderer = GetRenderer() as MultiPlayerInventoryRenderer;
        if (renderer != null)
        {
            var multiPriority = priority == InventoryModelRenderer.RenderPriority.High ? 
                               MultiPlayerInventoryRenderer.RenderPriority.High : 
                               MultiPlayerInventoryRenderer.RenderPriority.Normal;
            
            renderer.RequestRender(itemSO, onComplete, playerID, useCache, multiPriority);
        }
        else
        {
            Debug.LogError("Multi-player renderer not available");
            onComplete?.Invoke(null);
        }
    }
    
    /// <summary>
    /// Gets cached texture from the appropriate renderer.
    /// </summary>
    public static RenderTexture GetCachedTexture(ItemSO itemSO)
    {
        return GetRenderer()?.GetCachedTexture(itemSO);
    }
    
    /// <summary>
    /// Checks if an item has a cached texture.
    /// </summary>
    public static bool HasCachedTexture(ItemSO itemSO)
    {
        return GetRenderer()?.HasCachedTexture(itemSO) ?? false;
    }
    
    /// <summary>
    /// Clears the render cache.
    /// </summary>
    public static void ClearCache()
    {
        GetRenderer()?.ClearCache();
    }
    
    /// <summary>
    /// Checks if any rendering is currently in progress.
    /// </summary>
    public static bool IsRendering()
    {
        return GetRenderer()?.IsRendering() ?? false;
    }
    
    /// <summary>
    /// Gets the total queue size across all players.
    /// </summary>
    public static int GetQueueSize()
    {
        var renderer = GetRenderer();
        if (renderer is InventoryModelRenderer singleRenderer)
        {
            return singleRenderer.GetQueueSize();
        }
        else if (renderer is MultiPlayerInventoryRenderer multiRenderer)
        {
            return multiRenderer.GetQueueSize();
        }
        return 0;
    }
    
    /// <summary>
    /// Gets the queue size for a specific player (multi-player mode only).
    /// </summary>
    public static int GetPlayerQueueSize(string playerID)
    {
        if (!IsMultiPlayerMode) return GetQueueSize();
        
        var renderer = GetRenderer() as MultiPlayerInventoryRenderer;
        return renderer?.GetPlayerQueueSize(playerID) ?? 0;
    }
    
    /// <summary>
    /// Forces a refresh of the multi-player mode detection.
    /// Call this when the game mode changes during runtime.
    /// </summary>
    public static void RefreshModeDetection()
    {
        _isMultiPlayerMode = null;
    }
    
    /// <summary>
    /// Detects if the game is running in multi-player mode using the configuration system.
    /// </summary>
    private static bool DetectMultiPlayerMode()
    {
        // Use the configuration system to determine the mode
        return InventoryRenderingConfig.Instance.ShouldUseMultiPlayerRendering();
    }
    
    /// <summary>
    /// Attempts to determine the current player context for rendering requests.
    /// </summary>
    private static string GetCurrentPlayerContext()
    {
        // Try to get the player from the current UI context
        // This is a simplified approach - you might need more sophisticated context detection
        
        // Method 1: Check if there's a main user
        if (PlayersManager.Instance?.MainUser != null)
        {
            return PlayersManager.Instance.MainUser.UserID;
        }
        
        // Method 2: Get the first active user
        if (PlayersManager.Instance?.ActiveUsers.Count > 0)
        {
            return PlayersManager.Instance.ActiveUsers[0].UserID;
        }
        
        // Method 3: Try to find player from current inventory UI context
        // This would require additional context tracking in your UI system
        
        // Fallback
        return "default_player";
    }
    
    /// <summary>
    /// Gets the player ID associated with a specific inventory component.
    /// </summary>
    public static string GetPlayerIDFromInventory(Inventory inventory)
    {
        if (inventory == null) return GetCurrentPlayerContext();
        
        // Try to find the player that owns this inventory
        if (PlayersManager.Instance != null)
        {
            foreach (var user in PlayersManager.Instance.ActiveUsers)
            {
                if (user.PlayerCharacterComponent?.inventory == inventory)
                {
                    return user.UserID;
                }
            }
        }
        
        return GetCurrentPlayerContext();
    }
    
    /// <summary>
    /// Gets the player ID from an inventory slot UI component.
    /// This method can be used by InventorySlotUI to determine which player's render area to use.
    /// </summary>
    public static string GetPlayerIDFromSlotUI(MonoBehaviour slotUI)
    {
        if (slotUI == null) return GetCurrentPlayerContext();
        
        // Try to find the inventory this slot belongs to
        Inventory inventory = slotUI.GetComponentInParent<Inventory>();
        if (inventory != null)
        {
            return GetPlayerIDFromInventory(inventory);
        }
        
        // Try to find a player component in the hierarchy
        PlayerMonoBehaviour player = slotUI.GetComponentInParent<PlayerMonoBehaviour>();
        if (player?.User != null)
        {
            return player.User.UserID;
        }
        
        return GetCurrentPlayerContext();
    }
}

/// <summary>
/// Extension methods for easier integration with existing inventory UI components.
/// </summary>
public static class InventoryUIExtensions
{
    /// <summary>
    /// Requests rendering for an inventory slot with automatic player detection.
    /// </summary>
    public static void RequestRender(this InventorySlotUI slotUI, ItemSO itemSO, 
                                   System.Action<RenderTexture> onComplete, bool useCache = true, 
                                   InventoryModelRenderer.RenderPriority priority = InventoryModelRenderer.RenderPriority.Normal)
    {
        if (InventoryRendererAdapter.IsMultiPlayerMode)
        {
            string playerID = InventoryRendererAdapter.GetPlayerIDFromSlotUI(slotUI);
            InventoryRendererAdapter.RequestRenderForPlayer(itemSO, onComplete, playerID, useCache, priority);
        }
        else
        {
            InventoryRendererAdapter.RequestRender(itemSO, onComplete, useCache, priority);
        }
    }
}
