using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditor.Animations;
using System.IO;
using Newtonsoft.Json;


[System.Serializable]
public partial class PlayerMonoBehaviour : MonoBehaviour, IPlayer
{   
  public string UserID { get; private set; }
  public UserMonoBehaviour User;

  //Referencias a scripts

  public PlayerMovement movement;
  public PlayerStatus status;
  public FPSController firstPersonCamera;
  public Inventory inventory;
  public PlayerInputHandler MyInput;
  public DynamicIKManager playerIk;

  //Referencias a componentes  
  [JsonIgnore] public Rigidbody PlayerRigidbody;
  [JsonIgnore] public Transform PlayerCameraHolder;
  [JsonIgnore] public Camera PlayerCamera;
  [JsonIgnore] public Camera CanvasCamera;
  [JsonIgnore] public Transform UIHolder;
  [JsonIgnore] public Animator animator;
  [JsonIgnore] public AnimatorController ControllerAtual;
  [JsonIgnore] public AnimatorController ControllerBase;

  public InputCallConfig MenuBinding;

  public void Awake()
  {
    registerInputHandler();

    if(User == null) Debug.Log(" user is null");
    if(User.MyEventSystem == null) Debug.Log(" user.MyEventSystem is null");
    if(User.MyEventSystem.playerRoot == null) Debug.Log(" user.MyEventSystem.playerRoot is null");

    User.MyEventSystem.playerRoot = gameObject;
    Debug.Log($"PlayerCharacter initialized for UserID: {UserID}");
    Debug.Log($"PlayerCharacter initialized : {User.MyEventSystem.playerRoot.name}");
    Debug.Log($"PlayerCharacter initialized my name: {UIHolder.name}");
  }

  // Call this method from PlayerManager when the character is spawned
  public void Initialize(string userId, UserMonoBehaviour userMonoBehaviour)
  {
    User = userMonoBehaviour;
    this.UserID = userId;
    // Atribui o PlayerInputHandler do User, se ainda não estiver atribuído
    // ou se esta é a principal forma de garantir que está correto.
    if (this.MyInput == null && User != null)
    {
      this.MyInput = User.MyInput;
    }
    User.MyEventSystem.playerRoot = UIHolder.gameObject;
    registerInputHandler();
    Debug.Log($"PlayerCharacter initialized for UserID: {UserID}");
  }

  public void registerInputHandler()
  {
    movement.MyInput = MyInput;
    if (firstPersonCamera != null) firstPersonCamera.MyInput = MyInput; // Adicionado null check
    if (MyInput != null) // Adicionado null check
    {
      MyInput.RegisterCallback(this.OpenMenu, MenuBinding);
    }
  }

  public void OpenMenu(ActionActivationContext state)
  {
    UserMenuManager MenuManager = User.GetComponent<UserMenuManager>();
    MenuManager.OpenMenu();
  }

  public T GetComponentReference<T>() where T : class
  {
    if (typeof(T) == typeof(PlayerInputHandler)) return MyInput as T;
    if (typeof(T) == typeof(DynamicIKManager)) return playerIk as T;
    if (typeof(T) == typeof(FPSController)) return firstPersonCamera as T;
    if (typeof(T) == typeof(PlayerStatus)) return status as T;
    if (typeof(T) == typeof(PlayerMovement)) return movement as T;
    if (typeof(T) == typeof(Camera)) return PlayerCamera as T;
    if (typeof(T) == typeof(Animator)) return animator as T;

    Debug.LogError($"Component of type {typeof(T).Name} not found in PlayerMonoBehaviour.");
    return null;
  }
}