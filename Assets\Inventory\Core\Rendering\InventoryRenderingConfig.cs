using UnityEngine;

/// <summary>
/// Configuration component for inventory rendering system.
/// Manages settings for both single-player and multi-player scenarios.
/// </summary>
[CreateAssetMenu(fileName = "InventoryRenderingConfig", menuName = "Inventory/Rendering Config")]
public class InventoryRenderingConfig : ScriptableObject
{
    [Header("Mode Detection")]
    [SerializeField] private bool forceMultiPlayerMode = false;
    [SerializeField] private bool autoDetectMode = true;
    [Tooltip("Minimum number of players to automatically enable multi-player rendering")]
    [SerializeField] private int multiPlayerThreshold = 2;
    
    [Header("Multi-Player Settings")]
    [SerializeField] private int maxSupportedPlayers = 4;
    [SerializeField] private float playerAreaSeparation = 2000f;
    [SerializeField] private Vector3 baseRenderPosition = new Vector3(10000, 10000, 10000);
    
    [Header("Performance Settings")]
    [SerializeField] private int maxRendersPerFramePerPlayer = 2;
    [SerializeField] private int maxTotalRendersPerFrame = 6;
    [SerializeField] private int singlePlayerMaxRendersPerFrame = 3;
    
    [Header("Cache Settings")]
    [SerializeField] private int singlePlayerCacheSize = 100;
    [SerializeField] private int multiPlayerCacheSize = 200;
    [SerializeField] private float cacheExpirationTime = 300f;
    [SerializeField] private bool enablePersistentCache = false;
    
    [Header("Render Quality")]
    [SerializeField] private int renderTextureSize = 256;
    [SerializeField] private RenderTextureFormat renderTextureFormat = RenderTextureFormat.ARGB32;
    [SerializeField] private int renderTextureDepth = 24;
    
    [Header("Isolation Validation")]
    [SerializeField] private bool validateIsolation = true;
    [SerializeField] private float minimumIsolationDistance = 1000f;
    [SerializeField] private bool logIsolationWarnings = true;
    
    // Singleton instance
    private static InventoryRenderingConfig _instance;
    public static InventoryRenderingConfig Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = Resources.Load<InventoryRenderingConfig>("InventoryRenderingConfig");
                if (_instance == null)
                {
                    Debug.LogWarning("No InventoryRenderingConfig found in Resources. Creating default config.");
                    _instance = CreateDefaultConfig();
                }
            }
            return _instance;
        }
    }
    
    // Public properties
    public bool ForceMultiPlayerMode => forceMultiPlayerMode;
    public bool AutoDetectMode => autoDetectMode;
    public int MultiPlayerThreshold => multiPlayerThreshold;
    public int MaxSupportedPlayers => maxSupportedPlayers;
    public float PlayerAreaSeparation => playerAreaSeparation;
    public Vector3 BaseRenderPosition => baseRenderPosition;
    public int MaxRendersPerFramePerPlayer => maxRendersPerFramePerPlayer;
    public int MaxTotalRendersPerFrame => maxTotalRendersPerFrame;
    public int SinglePlayerMaxRendersPerFrame => singlePlayerMaxRendersPerFrame;
    public int SinglePlayerCacheSize => singlePlayerCacheSize;
    public int MultiPlayerCacheSize => multiPlayerCacheSize;
    public float CacheExpirationTime => cacheExpirationTime;
    public bool EnablePersistentCache => enablePersistentCache;
    public int RenderTextureSize => renderTextureSize;
    public RenderTextureFormat RenderTextureFormat => renderTextureFormat;
    public int RenderTextureDepth => renderTextureDepth;
    public bool ValidateIsolation => validateIsolation;
    public float MinimumIsolationDistance => minimumIsolationDistance;
    public bool LogIsolationWarnings => logIsolationWarnings;
    
    /// <summary>
    /// Determines if multi-player rendering should be used based on current configuration and game state.
    /// </summary>
    public bool ShouldUseMultiPlayerRendering()
    {
        if (forceMultiPlayerMode)
        {
            return true;
        }
        
        if (!autoDetectMode)
        {
            return false;
        }
        
        // Auto-detect based on active players
        if (PlayersManager.Instance != null)
        {
            int activePlayerCount = PlayersManager.Instance.ActiveUsers.Count;
            return activePlayerCount >= multiPlayerThreshold;
        }
        
        return false;
    }
    
    /// <summary>
    /// Gets the appropriate cache size based on the current mode.
    /// </summary>
    public int GetCacheSize()
    {
        return ShouldUseMultiPlayerRendering() ? multiPlayerCacheSize : singlePlayerCacheSize;
    }
    
    /// <summary>
    /// Gets the appropriate max renders per frame based on the current mode.
    /// </summary>
    public int GetMaxRendersPerFrame()
    {
        return ShouldUseMultiPlayerRendering() ? maxTotalRendersPerFrame : singlePlayerMaxRendersPerFrame;
    }
    
    /// <summary>
    /// Calculates the render position for a specific player.
    /// </summary>
    public Vector3 GetPlayerRenderPosition(int playerIndex)
    {
        if (!ShouldUseMultiPlayerRendering())
        {
            return baseRenderPosition;
        }
        
        // Create isolated positions for each player
        Vector3[] directions = {
            Vector3.right,      // Player 0: +X
            Vector3.left,       // Player 1: -X  
            Vector3.forward,    // Player 2: +Z
            Vector3.back        // Player 3: -Z
        };
        
        int directionIndex = playerIndex % directions.Length;
        Vector3 direction = directions[directionIndex];
        
        // Add vertical separation for additional players
        float verticalOffset = (playerIndex / directions.Length) * playerAreaSeparation;
        
        return baseRenderPosition + (direction * playerAreaSeparation) + (Vector3.up * verticalOffset);
    }
    
    /// <summary>
    /// Validates that a render position is adequately isolated.
    /// </summary>
    public bool ValidateRenderPosition(Vector3 position, int playerIndex = -1)
    {
        if (!validateIsolation)
            return true;
        
        // Check distance from origin (game world center)
        float distanceFromOrigin = Vector3.Distance(position, Vector3.zero);
        if (distanceFromOrigin < minimumIsolationDistance)
        {
            if (logIsolationWarnings)
            {
                Debug.LogWarning($"Render position {position} may be too close to game world. Distance: {distanceFromOrigin:F1}");
            }
            return false;
        }
        
        // In multi-player mode, check distance from other player positions
        if (ShouldUseMultiPlayerRendering() && playerIndex >= 0)
        {
            for (int i = 0; i < maxSupportedPlayers; i++)
            {
                if (i == playerIndex) continue;
                
                Vector3 otherPosition = GetPlayerRenderPosition(i);
                float distance = Vector3.Distance(position, otherPosition);
                
                if (distance < playerAreaSeparation * 0.5f) // Minimum 50% of separation distance
                {
                    if (logIsolationWarnings)
                    {
                        Debug.LogWarning($"Player {playerIndex} render position too close to player {i}. Distance: {distance:F1}");
                    }
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// Creates a default configuration instance.
    /// </summary>
    private static InventoryRenderingConfig CreateDefaultConfig()
    {
        var config = CreateInstance<InventoryRenderingConfig>();
        
        // Set default values
        config.forceMultiPlayerMode = false;
        config.autoDetectMode = true;
        config.multiPlayerThreshold = 2;
        config.maxSupportedPlayers = 4;
        config.playerAreaSeparation = 2000f;
        config.baseRenderPosition = new Vector3(10000, 10000, 10000);
        config.maxRendersPerFramePerPlayer = 2;
        config.maxTotalRendersPerFrame = 6;
        config.singlePlayerMaxRendersPerFrame = 3;
        config.singlePlayerCacheSize = 100;
        config.multiPlayerCacheSize = 200;
        config.cacheExpirationTime = 300f;
        config.enablePersistentCache = false;
        config.renderTextureSize = 256;
        config.renderTextureFormat = RenderTextureFormat.ARGB32;
        config.renderTextureDepth = 24;
        config.validateIsolation = true;
        config.minimumIsolationDistance = 1000f;
        config.logIsolationWarnings = true;
        
        return config;
    }
    
    /// <summary>
    /// Logs the current configuration for debugging.
    /// </summary>
    [ContextMenu("Log Current Configuration")]
    public void LogConfiguration()
    {
        Debug.Log($"Inventory Rendering Configuration:\n" +
                 $"Mode: {(ShouldUseMultiPlayerRendering() ? "Multi-Player" : "Single-Player")}\n" +
                 $"Max Players: {maxSupportedPlayers}\n" +
                 $"Cache Size: {GetCacheSize()}\n" +
                 $"Max Renders/Frame: {GetMaxRendersPerFrame()}\n" +
                 $"Texture Size: {renderTextureSize}x{renderTextureSize}\n" +
                 $"Base Position: {baseRenderPosition}\n" +
                 $"Player Separation: {playerAreaSeparation}");
    }
    
    void OnValidate()
    {
        // Clamp values to reasonable ranges
        maxSupportedPlayers = Mathf.Clamp(maxSupportedPlayers, 1, 8);
        multiPlayerThreshold = Mathf.Clamp(multiPlayerThreshold, 1, maxSupportedPlayers);
        playerAreaSeparation = Mathf.Clamp(playerAreaSeparation, 500f, 10000f);
        maxRendersPerFramePerPlayer = Mathf.Clamp(maxRendersPerFramePerPlayer, 1, 5);
        maxTotalRendersPerFrame = Mathf.Clamp(maxTotalRendersPerFrame, 1, 20);
        singlePlayerMaxRendersPerFrame = Mathf.Clamp(singlePlayerMaxRendersPerFrame, 1, 10);
        singlePlayerCacheSize = Mathf.Clamp(singlePlayerCacheSize, 10, 500);
        multiPlayerCacheSize = Mathf.Clamp(multiPlayerCacheSize, 50, 1000);
        cacheExpirationTime = Mathf.Clamp(cacheExpirationTime, 60f, 3600f);
        renderTextureSize = Mathf.ClosestPowerOfTwo(Mathf.Clamp(renderTextureSize, 64, 1024));
        renderTextureDepth = Mathf.Clamp(renderTextureDepth, 16, 32);
        minimumIsolationDistance = Mathf.Clamp(minimumIsolationDistance, 100f, 5000f);
    }
}
