# Multi-Player Inventory Rendering System

## Overview

The inventory rendering system has been enhanced to support multiple players simultaneously while maintaining backward compatibility with single-player scenarios. The system automatically detects the game mode and uses the appropriate rendering strategy.

## Key Features

### 🎮 **Automatic Mode Detection**
- Automatically switches between single-player and multi-player rendering based on active player count
- Configurable thresholds and manual override options
- Seamless transition without code changes in existing inventory UI

### 🏗️ **Isolated Render Areas**
- Each player gets a dedicated render area positioned far from the game world
- Automatic positioning with configurable separation distances
- Validation system to ensure adequate isolation between players

### ⚡ **Performance Optimization**
- Per-player render queues with priority support
- Configurable frame limits per player and total system
- Shared cache system for efficiency while maintaining isolation

### 🔧 **Configuration System**
- Centralized configuration through ScriptableObject
- Runtime and editor-time validation
- Comprehensive debugging and monitoring tools

## Architecture

### Core Components

1. **MultiPlayerInventoryRenderer** - Main multi-player rendering system
2. **InventoryRendererAdapter** - Automatic mode selection and unified API
3. **InventoryRenderingConfig** - Configuration management
4. **MultiPlayerRenderDebugger** - Editor debugging tools

### Compatibility

The system maintains full backward compatibility:
- Existing `InventorySlotUI` components work without modification
- Legacy single-player renderer remains available
- Automatic fallback for unsupported scenarios

## Usage

### Basic Usage (Automatic)

```csharp
// Existing code continues to work unchanged
InventoryRendererAdapter.RequestRender(itemSO, OnRenderComplete);
```

### Multi-Player Specific Usage

```csharp
// Request render for specific player
InventoryRendererAdapter.RequestRenderForPlayer(itemSO, OnRenderComplete, playerID);

// Get player-specific queue size
int queueSize = InventoryRendererAdapter.GetPlayerQueueSize(playerID);
```

### Configuration

Create a configuration asset:
1. Right-click in Project → Create → Inventory → Rendering Config
2. Place in Resources folder as "InventoryRenderingConfig"
3. Configure settings as needed

## Multi-Player Considerations

### 1. **Render Area Isolation**

Each player gets a render area positioned at:
- Player 0: Base position + X direction
- Player 1: Base position - X direction  
- Player 2: Base position + Z direction
- Player 3: Base position - Z direction
- Additional players: Vertical stacking

Default base position: `(10000, 10000, 10000)`
Default separation: `2000 units`

### 2. **Performance Management**

**Per-Player Limits:**
- Max renders per frame per player: 2
- Individual render queues with priority support
- Isolated render components (camera, lighting, containers)

**System-Wide Limits:**
- Max total renders per frame: 6
- Shared cache for memory efficiency
- Automatic queue processing with fairness

### 3. **Cache Strategy**

**Shared Cache Benefits:**
- Memory efficiency (identical items cached once)
- Faster rendering for common items
- Configurable size and expiration

**Cache Key Strategy:**
- Items cached by ItemSO reference
- No player-specific caching needed for identical items
- Automatic cleanup and LRU eviction

## Configuration Options

### Mode Detection
- `forceMultiPlayerMode`: Force multi-player rendering regardless of player count
- `autoDetectMode`: Automatically detect based on active players
- `multiPlayerThreshold`: Minimum players to trigger multi-player mode

### Performance Settings
- `maxRendersPerFramePerPlayer`: Limit per player per frame
- `maxTotalRendersPerFrame`: System-wide frame limit
- `singlePlayerMaxRendersPerFrame`: Single-player mode limit

### Isolation Settings
- `playerAreaSeparation`: Distance between player render areas
- `baseRenderPosition`: Starting position for render areas
- `validateIsolation`: Enable position validation
- `minimumIsolationDistance`: Minimum distance from game world

## Debugging and Monitoring

### Editor Tools

**Multi-Player Render Debugger Window:**
- Tools → Inventory → Multi-Player Render Debugger
- Real-time monitoring of all players
- Performance metrics and warnings
- Configuration validation
- Testing tools

**Debug Information:**
- Current mode (single/multi-player)
- Active player count and render positions
- Queue sizes per player
- Cache statistics
- Performance warnings

### Runtime Debugging

```csharp
// Check current mode
bool isMultiPlayer = InventoryRendererAdapter.IsMultiPlayerMode;

// Get queue information
int totalQueue = InventoryRendererAdapter.GetQueueSize();
int playerQueue = InventoryRendererAdapter.GetPlayerQueueSize(playerID);

// Validate configuration
InventoryRenderingConfig.Instance.LogConfiguration();
```

## Migration Guide

### From Single-Player System

No code changes required! The adapter automatically handles mode selection.

### Optimizing for Multi-Player

1. **Review render request frequency** - Avoid excessive requests
2. **Use priority rendering** for important UI elements
3. **Configure appropriate limits** based on target performance
4. **Monitor queue sizes** during peak usage

### Custom Player Context

If you need custom player detection logic:

```csharp
public static class CustomPlayerContext
{
    public static string GetPlayerIDFromContext(MonoBehaviour component)
    {
        // Your custom logic here
        return InventoryRendererAdapter.GetPlayerIDFromSlotUI(component);
    }
}
```

## Performance Recommendations

### For 2 Players
- Max renders per frame per player: 2
- Total max renders per frame: 4
- Cache size: 150-200 items

### For 4 Players
- Max renders per frame per player: 1-2
- Total max renders per frame: 6-8
- Cache size: 200-300 items

### Memory Considerations
- 256x256 RGBA texture ≈ 0.25 MB
- 200 item cache ≈ 50 MB
- Monitor memory usage in builds

## Troubleshooting

### Common Issues

**Mode not switching automatically:**
- Check `InventoryRenderingConfig.Instance.AutoDetectMode`
- Verify `PlayersManager.Instance.ActiveUsers.Count`
- Call `InventoryRendererAdapter.RefreshModeDetection()`

**Render positions too close:**
- Increase `playerAreaSeparation` in config
- Check `validateIsolation` warnings
- Use debug window to visualize positions

**Performance issues:**
- Reduce `maxRendersPerFramePerPlayer`
- Increase cache size and expiration time
- Monitor queue sizes in debug window

**Cache not working:**
- Ensure `useCache = true` in render requests
- Check cache size limits in configuration
- Verify ItemSO references are consistent

## Future Enhancements

Potential improvements for future versions:
- Dynamic render area allocation
- Player-specific cache partitioning
- Network synchronization for multiplayer games
- Advanced priority algorithms
- Render texture pooling
- LOD system for distant players

## API Reference

### InventoryRendererAdapter
- `RequestRender()` - Standard render request with auto-detection
- `RequestRenderForPlayer()` - Player-specific render request
- `GetCachedTexture()` - Retrieve cached texture
- `IsMultiPlayerMode` - Check current mode
- `RefreshModeDetection()` - Force mode re-detection

### MultiPlayerInventoryRenderer
- `RequestRender()` - Multi-player render request
- `GetPlayerQueueSize()` - Get queue size for specific player
- `GetQueueSize()` - Get total queue size across all players

### InventoryRenderingConfig
- `ShouldUseMultiPlayerRendering()` - Mode detection logic
- `GetPlayerRenderPosition()` - Calculate player render position
- `ValidateRenderPosition()` - Validate position isolation
- `LogConfiguration()` - Debug configuration output
