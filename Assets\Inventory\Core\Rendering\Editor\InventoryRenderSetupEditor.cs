using UnityEngine;
using UnityEditor;

#if UNITY_EDITOR

/// <summary>
/// Custom inspector for InventoryRenderSetup component.
/// Provides a user-friendly interface for configuring the inventory rendering system.
/// </summary>
[CustomEditor(typeof(InventoryRenderSetup))]
public class InventoryRenderSetupEditor : Editor
{
    private InventoryRenderSetup setup;
    private bool showAdvancedSettings = false;
    private bool showTestingTools = false;
    
    void OnEnable()
    {
        setup = (InventoryRenderSetup)target;
    }
    
    public override void OnInspectorGUI()
    {
        serializedObject.Update();
        
        // Header
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Inventory Render System Setup", EditorStyles.boldLabel);
        EditorGUILayout.HelpBox("This component helps you configure and test the isolated inventory rendering system.", MessageType.Info);
        
        EditorGUILayout.Space();
        
        // Quick setup section
        DrawQuickSetupSection();
        
        EditorGUILayout.Space();
        
        // Basic settings
        DrawBasicSettings();
        
        EditorGUILayout.Space();
        
        // Advanced settings (foldout)
        showAdvancedSettings = EditorGUILayout.Foldout(showAdvancedSettings, "Advanced Settings", true);
        if (showAdvancedSettings)
        {
            EditorGUI.indentLevel++;
            DrawAdvancedSettings();
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // Testing tools (foldout)
        showTestingTools = EditorGUILayout.Foldout(showTestingTools, "Testing Tools", true);
        if (showTestingTools)
        {
            EditorGUI.indentLevel++;
            DrawTestingTools();
            EditorGUI.indentLevel--;
        }
        
        EditorGUILayout.Space();
        
        // System status
        DrawSystemStatus();
        
        serializedObject.ApplyModifiedProperties();
    }
    
    void DrawQuickSetupSection()
    {
        EditorGUILayout.LabelField("Quick Setup", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("Initialize System"))
        {
            setup.InitializeRenderSystem();
        }
        if (GUILayout.Button("Open Debugger"))
        {
            InventoryRenderDebugger.ShowWindow();
        }
        EditorGUILayout.EndHorizontal();
        
        if (Application.isPlaying)
        {
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Clear Cache"))
            {
                setup.ClearCache();
            }
            if (GUILayout.Button("Print Stats"))
            {
                setup.PrintCacheStats();
            }
            EditorGUILayout.EndHorizontal();
        }
    }
    
    void DrawBasicSettings()
    {
        EditorGUILayout.LabelField("Basic Settings", EditorStyles.boldLabel);
        
        // Render area configuration
        EditorGUILayout.PropertyField(serializedObject.FindProperty("useCustomPosition"));
        if (serializedObject.FindProperty("useCustomPosition").boolValue)
        {
            EditorGUILayout.PropertyField(serializedObject.FindProperty("customRenderPosition"));
        }
        else
        {
            EditorGUILayout.PropertyField(serializedObject.FindProperty("findOptimalPosition"));
        }
        
        EditorGUILayout.Space();
        
        // Render quality settings
        EditorGUILayout.PropertyField(serializedObject.FindProperty("renderTextureSize"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("textureFormat"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("backgroundColor"));
    }
    
    void DrawAdvancedSettings()
    {
        // Camera settings
        EditorGUILayout.LabelField("Camera Settings", EditorStyles.miniBoldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("fieldOfView"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("cameraThreshold"));
        
        EditorGUILayout.Space();
        
        // Lighting settings
        EditorGUILayout.LabelField("Lighting Settings", EditorStyles.miniBoldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("lightColor"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("lightIntensity"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("lightRotation"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("enableFillLighting"));
        
        EditorGUILayout.Space();
        
        // Cache settings
        EditorGUILayout.LabelField("Cache Settings", EditorStyles.miniBoldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxCacheSize"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("cacheExpirationTime"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("enablePersistentCache"));
        
        EditorGUILayout.Space();
        
        // Performance settings
        EditorGUILayout.LabelField("Performance Settings", EditorStyles.miniBoldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxRendersPerFrame"));
        EditorGUILayout.PropertyField(serializedObject.FindProperty("maxMemoryUsage"));
    }
    
    void DrawTestingTools()
    {
        EditorGUILayout.LabelField("Test Items", EditorStyles.miniBoldLabel);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("testItems"), true);
        EditorGUILayout.PropertyField(serializedObject.FindProperty("preloadTestItems"));
        
        EditorGUILayout.Space();
        
        if (Application.isPlaying)
        {
            EditorGUILayout.LabelField("Runtime Testing", EditorStyles.miniBoldLabel);
            
            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Test All Items"))
            {
                setup.TestRenderAllItems();
            }
            if (GUILayout.Button("Validate Isolation"))
            {
                setup.ValidateIsolation();
            }
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.HelpBox("Enter play mode to access runtime testing tools.", MessageType.Info);
        }
    }
    
    void DrawSystemStatus()
    {
        EditorGUILayout.LabelField("System Status", EditorStyles.boldLabel);
        
        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("System status is only available in play mode.", MessageType.Info);
            return;
        }
        
        bool isSystemActive = InventoryModelRenderer.Instance != null;
        
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField("Renderer Status:");
        
        Color originalColor = GUI.color;
        GUI.color = isSystemActive ? Color.green : Color.red;
        EditorGUILayout.LabelField(isSystemActive ? "Active" : "Inactive", EditorStyles.boldLabel);
        GUI.color = originalColor;
        
        EditorGUILayout.EndHorizontal();
        
        if (isSystemActive)
        {
            var renderer = InventoryModelRenderer.Instance;
            EditorGUILayout.LabelField($"Queue Size: {renderer.GetQueueSize()}");
            EditorGUILayout.LabelField($"Is Rendering: {renderer.IsRendering()}");
            
            var stats = renderer.GetCacheStats();
            EditorGUILayout.LabelField($"Cached Items: {stats.entryCount}");
            EditorGUILayout.LabelField($"Memory Usage: {stats.memoryUsage / (1024 * 1024):F1} MB");
        }
    }
    
    void OnSceneGUI()
    {
        // Draw render area visualization in scene view
        if (setup.UseCustomPosition)
        {
            Vector3 renderPos = setup.CustomRenderPosition;
            
            Handles.color = Color.cyan;
            Handles.DrawWireCube(renderPos, Vector3.one * 10f);
            
            // Draw label
            Handles.Label(renderPos + Vector3.up * 6f, "Inventory Render Area", EditorStyles.boldLabel);
            
            // Allow position editing
            EditorGUI.BeginChangeCheck();
            Vector3 newPos = Handles.PositionHandle(renderPos, Quaternion.identity);
            if (EditorGUI.EndChangeCheck())
            {
                Undo.RecordObject(setup, "Move Render Area");
                SerializedProperty customPosProp = serializedObject.FindProperty("customRenderPosition");
                customPosProp.vector3Value = newPos;
                serializedObject.ApplyModifiedProperties();
                EditorUtility.SetDirty(setup);
            }
        }
    }
}

#endif
