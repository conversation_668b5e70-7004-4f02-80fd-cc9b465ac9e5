using UnityEngine;
using UnityEngine.UI;

[System.Serializable]
public class RectTransformUtils
{
    public static Quaternion SyncRotation(GameObject model, RectTransform rectTransform)
    {
        // Note: model parameter kept for API compatibility but not currently used
        // Future enhancement could use model's intrinsic orientation

        // Obtém as dimensões do RectTransform
        Vector2 rectSize = rectTransform.sizeDelta;

        // Determina a direção do maior eixo do RectTransform
        Vector3 direction = rectSize.x > rectSize.y ? Vector3.right : Vector3.up;

        // Calcula a nova rotação para alinhar o maior eixo da malha com a direção do maior eixo do RectTransform
        Quaternion targetRotation = Quaternion.FromToRotation(Vector3.up, direction);

        return targetRotation;
    }

    /// <summary>
    /// Legacy method - fits a GameObject to a RectTransform using exact positioning
    /// </summary>
    public static void FitGameObject(RectTransform rectTransform, GameObject gameObject)
    {
        
    }

    /// <summary>
    /// Legacy method - fits a GameObject to a RectTransform using ModelInfo
    /// </summary>
    public static void FitGameObject(RectTransform rectTransform, GameObject targetModel, ModelInfo modelInfo, float scaleThreshold = 1.0f)
    {
        if (targetModel == null || modelInfo == null || rectTransform == null)
            return;

        Vector2 rectSize = rectTransform.rect.size;
        Vector3 meshCenterLocal = Vector3.zero;
        float meshRadius = 0f;

        // Escolhe bounds e calcula o centro da malha
        if (modelInfo.useAutoBounds)
        {
            // Chame a nova função que retorna o centro e o raio corretamente
            meshCenterLocal = CalculateLocalMeshBoundsAndRadius(targetModel, out meshRadius);
        }
        else
        {
            meshRadius = modelInfo.meshBoundsRadius;
            meshCenterLocal = modelInfo.centerOffset;
        }

        Debug.Log($"MeshRectAdapter: Updating scale with meshRadius {meshRadius} for rectSize {rectSize}");

        // Protege contra zero
        const float kMin = 0.001f;
        meshRadius = Mathf.Max(meshRadius, kMin);

        // Reduz o tamanho do rectSize disponível para o cálculo da escala pelo threshold
        Vector2 adjustedRectSize = rectSize * scaleThreshold;

        // Calcula uniformScale usando o diâmetro (2 * raio)
        float diameter = 2 * meshRadius;
        float scaleX = adjustedRectSize.x / (diameter * modelInfo.scaleRatio.x);
        float scaleY = adjustedRectSize.y / (diameter * modelInfo.scaleRatio.y);
        float uniform = Mathf.Min(scaleX, scaleY);

        // Limita a um teto razoável (opcional)
        uniform = Mathf.Clamp(uniform, 0.01f, 1000f);

        targetModel.transform.localScale = new Vector3(
            uniform * modelInfo.scaleRatio.x,
            uniform * modelInfo.scaleRatio.y,
            uniform * modelInfo.scaleRatio.z
        );

        targetModel.transform.localEulerAngles = modelInfo.defaultRotation;

        Vector3 desiredLocalPosition = -meshCenterLocal;

        Vector3 scaledOffset = new Vector3(
            desiredLocalPosition.x * targetModel.transform.localScale.x,
            desiredLocalPosition.y * targetModel.transform.localScale.y,
            desiredLocalPosition.z * targetModel.transform.localScale.z
        );

        // Agora, aplique a rotação DO PRÓPRIO targetModel ao offset escalado.
        // Isso garante que o movimento para centralizar leve em conta a orientação final do modelo.
        targetModel.transform.localPosition = targetModel.transform.localRotation * scaledOffset;
    }

     /// <summary>
    /// Calcula o centro local da malha e o raio da esfera circunscrita para todos os vértices.
    /// Retorna o centro local da AABB do modelo e, por parâmetro out, o raio.
    /// </summary>
    public static Vector3 CalculateLocalMeshBoundsAndRadius(GameObject go, out float radius)
    {
        MeshFilter[] filters = go.GetComponentsInChildren<MeshFilter>();
        SkinnedMeshRenderer[] skinnedRenderers = go.GetComponentsInChildren<SkinnedMeshRenderer>();

        if (filters.Length == 0 && skinnedRenderers.Length == 0)
        {
            Debug.LogWarning($"MeshRectAdapter: No mesh components found on {go.name}, using fallback bounds");
            radius = 0f; // Ou um valor padrão razoável
            return Vector3.zero; // Ou um valor padrão
        }

        Vector3 min = Vector3.positiveInfinity;
        Vector3 max = Vector3.negativeInfinity;

        // Coletar todos os pontos
        foreach (var filter in filters)
        {
            if (filter.sharedMesh == null || !filter.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = filter.sharedMesh.vertices;
            Transform meshTransform = filter.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        foreach (var skinnedRenderer in skinnedRenderers)
        {
            if (skinnedRenderer.sharedMesh == null || !skinnedRenderer.gameObject.activeInHierarchy) continue;

            Vector3[] vertices = skinnedRenderer.sharedMesh.vertices;
            Transform meshTransform = skinnedRenderer.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 localPoint = go.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, localPoint);
                max = Vector3.Max(max, localPoint);
            }
        }

        if (min == Vector3.positiveInfinity || max == Vector3.negativeInfinity)
        {
            radius = 0f;
            return Vector3.zero;
        }

        // Calcular o centro da AABB
        Vector3 center = (min + max) * 0.5f;

        // Calcular o raio da esfera que envolve a AABB
        // A distância do centro a qualquer um dos cantos da AABB é o raio da esfera circunscrita
        Vector3 extents = (max - min) * 0.5f;
        radius = extents.magnitude; // A magnitude do vetor de extents é o raio da esfera

        return center;
    }
    
    public static void SyncAnchor(RectTransform rectTransform, GridLayoutGroup gridLayoutGroup)
    {
        if (rectTransform == null || gridLayoutGroup == null)
        {
            Debug.LogError("Os parâmetros rectTransform e gridLayoutGroup não podem ser nulos.");
        }

        TextAnchor anchor = gridLayoutGroup.childAlignment;

        Vector2[] anchorPoints = GetAnchorPoints(anchor);
        Vector2 anchorMin = anchorPoints[0];
        Vector2 anchorMax = anchorPoints[1];

        // Atualizar o RectTransform
        rectTransform.anchorMin = anchorMin;
        rectTransform.anchorMax = anchorMax;
    }

    public static void SyncAnchor(RectTransform rectTransform, RectTransform child)
    {
        if (rectTransform == null || child == null)
        {
            Debug.LogError("Os parâmetros rectTransform e gridLayoutGroup não podem ser nulos.");
        }

        Vector2 anchorMin = child.anchorMin;
        Vector2 anchorMax = child.anchorMax;

        // Atualizar o RectTransform
        rectTransform.anchorMin = anchorMin;
        rectTransform.anchorMax = anchorMax;
    }

    public static Vector2[] GetAnchorPoints(TextAnchor anchor)
    {   
        Vector2 anchorMin;
        Vector2 anchorMax;

        Debug.Log($"Anchor: {anchor} - {anchor.GetHashCode()}");

        switch (anchor)
        {
            case TextAnchor.UpperLeft:
                anchorMin = new Vector2(0, 1);
                anchorMax = new Vector2(0, 1);
                break;
            case TextAnchor.UpperCenter:
                anchorMin = new Vector2(0.5f, 1);
                anchorMax = new Vector2(0.5f, 1);
                break;
            case TextAnchor.UpperRight:
                anchorMin = new Vector2(1, 1);
                anchorMax = new Vector2(1, 1);
                break;
            case TextAnchor.MiddleLeft:
                anchorMin = new Vector2(0, 0.5f);
                anchorMax = new Vector2(0, 0.5f);
                break;
            case TextAnchor.MiddleCenter:
                anchorMin = new Vector2(0.5f, 0.5f);
                anchorMax = new Vector2(0.5f, 0.5f);
                break;
            case TextAnchor.MiddleRight:
                anchorMin = new Vector2(1, 0.5f);
                anchorMax = new Vector2(1, 0.5f);
                break;
            case TextAnchor.LowerLeft:
                anchorMin = new Vector2(0, 0);
                anchorMax = new Vector2(0, 0);
                break;
            case TextAnchor.LowerCenter:
                anchorMin = new Vector2(0.5f, 0);
                anchorMax = new Vector2(0.5f, 0);
                break;
            case TextAnchor.LowerRight:
                anchorMin = new Vector2(1, 0);
                anchorMax = new Vector2(1, 0);
                break;
            default:
                Debug.LogError("Valor de childAlignment desconhecido.");
                return new Vector2[]{Vector2.zero, Vector2.zero};
        }
        return new Vector2[]{anchorMin, anchorMax};
    }
}