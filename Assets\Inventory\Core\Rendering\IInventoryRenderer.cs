using UnityEngine;

/// <summary>
/// Interface for inventory rendering systems.
/// Provides a contract for rendering item models to textures.
/// </summary>
public interface IInventoryRenderer
{
    /// <summary>
    /// Requests rendering of an item model to a render texture.
    /// </summary>
    /// <param name="itemSO">The item to render</param>
    /// <param name="onComplete">Callback when rendering is complete</param>
    /// <param name="useCache">Whether to use cached textures if available</param>
    void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, bool useCache = true);
    
    /// <summary>
    /// Gets a cached render texture for an item if available.
    /// </summary>
    /// <param name="itemSO">The item to get cached texture for</param>
    /// <returns>Cached render texture or null if not available</returns>
    RenderTexture GetCachedTexture(ItemSO itemSO);
    
    /// <summary>
    /// Checks if an item has a cached render texture.
    /// </summary>
    /// <param name="itemSO">The item to check</param>
    /// <returns>True if cached texture exists</returns>
    bool HasCachedTexture(ItemSO itemSO);
    
    /// <summary>
    /// Clears the render texture cache.
    /// </summary>
    void ClearCache();
    
    /// <summary>
    /// Gets whether the renderer is currently processing requests.
    /// </summary>
    /// <returns>True if currently rendering</returns>
    bool IsRendering();
}
