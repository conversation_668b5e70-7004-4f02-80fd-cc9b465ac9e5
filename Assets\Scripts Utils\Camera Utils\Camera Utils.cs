using UnityEngine;
using System;
using System.Collections;
using UnityEngine.Rendering;

public static class CameraUtils
{

    public static Vector3 FitOnCamera(GameObject gameObject, Camera camera, float threshold = 0.1f)
    {
        if (gameObject == null || camera == null)
        {
            Debug.LogError("GameObject or Camera cannot be null.");
            return Vector3.zero;
        }

        // Obter o centro e o raio da esfera circunscrita do objeto
        float meshRadius;
        Vector3 meshCenter = RectTransformUtils.CalculateLocalMeshBoundsAndRadius(gameObject, out meshRadius);

        // Adicionar a margem (threshold) ao raio para garantir que haja um espaço.
        // Isso garante que o threshold afete a distância da câmera.
        float boundingRadius = meshRadius * (1.0f + threshold);

        // A distância da câmera necessária para enquadrar a esfera inteira no campo de visão
        // A fórmula é: distância = raio / tan(ângulo_de_visão / 2)
        float distance = boundingRadius / Mathf.Tan(camera.fieldOfView * 0.5f * Mathf.Deg2Rad);

        // Encontra o ponto central do objeto no espaço global
        Vector3 targetPoint = gameObject.transform.TransformPoint(meshCenter);
        
        // A nova posição da câmera será no mesmo X e Y que o objeto, mas afastada no eixo Z.
        // Isso garante que a câmera esteja diretamente "de frente" para o objeto.
        Vector3 cameraPosition = new Vector3(targetPoint.x, targetPoint.y, targetPoint.z - distance);
        
        // Define a nova posição da câmera
        camera.transform.position = cameraPosition;

        // A rotação da câmera é ajustada para que ela "olhe" para o centro do objeto.
        // Isso é feito após o cálculo da posição para garantir o alinhamento correto.
        camera.transform.LookAt(targetPoint);
        
        return cameraPosition;
    }

    public static Texture2D CaptureCamera(Camera cam)
    {
        // Cria uma RenderTexture com as dimensões da tela
        RenderTexture rt = new RenderTexture(Screen.width, Screen.height, 24);
        cam.targetTexture = rt;

        // Renderiza a câmera
        Texture2D screenshot = new Texture2D(Screen.width, Screen.height, TextureFormat.RGB24, false);
        cam.Render();

        // Ativa a RenderTexture e lê os pixels
        RenderTexture.active = rt;
        screenshot.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
        screenshot.Apply();

        // Limpa as configurações
        cam.targetTexture = null;
        RenderTexture.active = null;

        // Libera a RenderTexture
        rt.Release();
        rt = null;

        return screenshot;
    }

    public static IEnumerator CaptureScreenshot(Action<Texture2D> callback)
    {        
        // Aguarda o fim do frame para garantir que a tela tenha sido renderizada completamente
        yield return new WaitForEndOfFrame();

        // Cria uma textura que corresponde às dimensões da tela
        Texture2D screenshot = new Texture2D(Screen.width, Screen.height, TextureFormat.RGB24, false);
        
        // Lê os pixels da tela e os armazena na textura
        screenshot.ReadPixels(new Rect(0, 0, Screen.width, Screen.height), 0, 0);
        screenshot.Apply();
        // Return the screenshot using the callback.
        callback?.Invoke(screenshot);
    }
}
