using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class InventorySlotUI : MonoBehaviour, IInventorySlotUI, IPointerEnterHandler, IPointerExit<PERSON><PERSON><PERSON>, IPointerClickHandler
{
    [Header("UI Components")]
    [SerializeField] RectTransform spawnItemParent;
    [SerializeField] GameObject itemModel;
    public Transform itemModelPivot;
    [SerializeField] public ItemSO itemSO;
    public IInventoryUI parentUI;

    [Tooltip("Image component to display the item's icon.")]
    [SerializeField] private RawImage itemIconModel;
    [SerializeField] private Image itemIcon;

    [Header("Model Rendering")]
    [SerializeField] private Camera renderCamera;
    [SerializeField] private Transform renderCameraPivot;
    public RenderTexture texture;

    [Tooltip("Text component to display the item's stack amount.")]
    [SerializeField] private TextMeshProUGUI amountText;

    enum ShowType {Model, Icon};
    [SerializeField] private ShowType showType = ShowType.Model;
    
    public System.Action<IInventorySlotUI, bool> OnSlotHovered { get; set; }
    public System.Action<IInventorySlotUI> OnSlotPressed { get; set; }

    public IInventoryUI GetParentUI() { return parentUI; }

    public void Awake()
    {
        GridLayoutGroup layoutGroup = GetComponentInParent<GridLayoutGroup>();
        if (layoutGroup != null)
        {
            Debug.Log("layout encontrado");
            layoutGroup.OnLayoutUpdate += UpdateModelPosition;
        }
    }

    /// <summary>
    /// Updates the slot's visual representation based on the slot data.
    /// </summary>
    /// <param name="slotData">The data from the inventory slot to display.</param>
    public void UpdateSlot(IInventorySlot slotData)
    {
        if (slotData != null && !slotData.IsEmpty())
        {
            itemSO = slotData.Item();
            switch (showType)
            {
                case ShowType.Model:
                    itemIcon.enabled = false;
                    itemIconModel.enabled = true;
                    ShowModel();
                    break;
                case ShowType.Icon:
                    itemIcon.enabled = true;
                    itemIconModel.enabled = false;
                    ShowIcon();
                    break;
            }

            // Mostra a quantidade apenas se o item for empilhável e tiver mais de 1
            if (slotData.Amount() > 1)
            {
                amountText.enabled = true;
                amountText.text = slotData.Amount().ToString();
            }
            else
            {
                amountText.enabled = false;
            }
        }
        else
        {
            // Se o slot estiver vazio, limpa a UI
            Clear();
        }
    }

    public void UpdateModelPosition()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            if(renderCamera == null)
            {
                GameObject cameraGOPivot = new GameObject("RenderCameraPivot of " + itemSO.name + "");
                renderCameraPivot = cameraGOPivot.transform;
                GameObject cameraGO = new GameObject("RenderCamera of " + itemSO.name);
                cameraGO.transform.parent = cameraGOPivot.transform;
                cameraGO.transform.localPosition = Vector3.zero;
                renderCamera = cameraGO.AddComponent<Camera>();
            }
            else
            {
                renderCamera.enabled = true;
                // Ajusta a câmera para enquadrar o modelo do item
                CameraUtils.FitOnCamera(itemModel, renderCamera);
            }
        }
    }

    public void OnDisable()
    {
        if (renderCamera != null) renderCamera.enabled = false;
    }

    [ContextMenu("Fit")]
    public void FitButton()
    {
        if (showType == ShowType.Model)
        {
            if(itemModel == null) return;
            Debug.Log("Update position");
            CameraUtils.FitOnCamera(itemModel, renderCamera);
        }
    }

    public void HoverSlot()
    {
        
    }

    public void ShowIcon()
    {
        itemIcon.sprite = itemSO.icon;
    }

    public void ShowModel()
    {
        // Limpa o modelo anterior, se houver
        if(itemModel != null)
        {
            Destroy(itemModelPivot);
            itemModelPivot = null;
            itemModel = null;
        }

        // Instancia o novo modelo
        itemModelPivot = new GameObject("Item Model Pivot " + itemSO.name).transform;
        itemModel = Instantiate(itemSO.prefab, itemModelPivot);
        Rigidbody rb = itemModel.GetComponentInChildren<Rigidbody>();
        Collider col = itemModel.GetComponentInChildren<Collider>();
        
        if (rb != null) rb.isKinematic = true;
        if (col != null) col.isTrigger = true;

        // Cria a RenderTexture e a associa à câmera
        if (texture == null)
        {
            texture = new RenderTexture(256, 256, 24, RenderTextureFormat.ARGB32);
        }
        itemIconModel.texture = texture;
    }

    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: " + this);    
        OnSlotHovered.Invoke(this, true);
    }

    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: 2 " + this);
        OnSlotHovered.Invoke(this, false);
    }

    public void OnPointerClick(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        if (OnSlotPressed != null) OnSlotPressed(this);
    }

    /// <summary>
    /// Clears the slot's visual representation, making it appear empty.
    /// </summary>
    public void Clear()
    {
        itemSO = null;
        itemIcon.enabled = false;
        itemIcon.sprite = null;
        amountText.enabled = false;
        amountText.text = "";
    }
}