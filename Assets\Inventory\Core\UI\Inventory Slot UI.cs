using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using TMPro;
using System.Collections.Generic;
using System.Linq;

public class InventorySlotUI : MonoBehaviour, IInventorySlotUI, IPointerEnterHandler, IPointerExit<PERSON><PERSON><PERSON>, IPointerClickHandler
{
    [Header("UI Components")]
    [SerializeField] RectTransform spawnItemParent;
    [SerializeField] GameObject itemModel;
    public Transform itemModelPivot;
    [SerializeField] public ItemSO itemSO;
    public IInventoryUI parentUI;

    [Tooltip("Image component to display the item's icon.")]
    [SerializeField] private RawImage itemIconModel;
    [SerializeField] private Image itemIcon;

    [Header("Model Rendering")]
    [SerializeField] private bool useHighPriorityRendering = false;
    public RenderTexture texture;
    private bool isRenderingRequested = false;

    [Tooltip("Text component to display the item's stack amount.")]
    [SerializeField] private TextMeshProUGUI amountText;

    enum ShowType {Model, Icon};
    [SerializeField] private ShowType showType = ShowType.Model;
    
    public System.Action<IInventorySlotUI, bool> OnSlotHovered { get; set; }
    public System.Action<IInventorySlotUI> OnSlotPressed { get; set; }

    public IInventoryUI GetParentUI() { return parentUI; }

    public void Awake()
    {
        // No longer need to track layout updates for individual cameras
        // The centralized renderer handles all positioning
    }

    /// <summary>
    /// Updates the slot's visual representation based on the slot data.
    /// </summary>
    /// <param name="slotData">The data from the inventory slot to display.</param>
    public void UpdateSlot(IInventorySlot slotData)
    {
        if (slotData != null && !slotData.IsEmpty())
        {
            itemSO = slotData.Item();
            switch (showType)
            {
                case ShowType.Model:
                    itemIcon.enabled = false;
                    itemIconModel.enabled = true;
                    RequestModelRender();
                    break;
                case ShowType.Icon:
                    itemIcon.enabled = true;
                    itemIconModel.enabled = false;
                    ShowIcon();
                    break;
            }

            // Mostra a quantidade apenas se o item for empilhável e tiver mais de 1
            if (slotData.Amount() > 1)
            {
                amountText.enabled = true;
                amountText.text = slotData.Amount().ToString();
            }
            else
            {
                amountText.enabled = false;
            }
        }
        else
        {
            // Se o slot estiver vazio, limpa a UI
            Clear();
        }
    }

    /// <summary>
    /// Requests rendering of the current item using the centralized renderer.
    /// </summary>
    private void RequestModelRender()
    {
        if (itemSO == null || isRenderingRequested)
            return;

        isRenderingRequested = true;

        // Determine priority based on slot settings
        var priority = useHighPriorityRendering ?
            InventoryModelRenderer.RenderPriority.High :
            InventoryModelRenderer.RenderPriority.Normal;

        // Request render from centralized system
        InventoryModelRenderer.Instance.RequestRender(
            itemSO,
            OnRenderComplete,
            useCache: true,
            priority: priority
        );
    }

    /// <summary>
    /// Called when the centralized renderer completes rendering this item.
    /// </summary>
    /// <param name="renderTexture">The rendered texture, or null if failed</param>
    private void OnRenderComplete(RenderTexture renderTexture)
    {
        isRenderingRequested = false;

        if (renderTexture != null)
        {
            texture = renderTexture;
            if (itemIconModel != null)
            {
                itemIconModel.texture = texture;
            }
        }
        else
        {
            Debug.LogWarning($"Failed to render item: {itemSO?.name}");
            // Fallback to icon if available
            if (itemSO?.icon != null)
            {
                ShowIcon();
            }
        }
    }

    public void OnDisable()
    {
        // No longer need to manage individual cameras
        isRenderingRequested = false;
    }

    [ContextMenu("Force Render")]
    public void ForceRender()
    {
        if (showType == ShowType.Model && itemSO != null)
        {
            isRenderingRequested = false; // Reset flag
            RequestModelRender();
        }
    }

    public void HoverSlot()
    {
        
    }

    public void ShowIcon()
    {
        itemIcon.sprite = itemSO.icon;
    }

    /// <summary>
    /// Legacy method - now redirects to centralized rendering system.
    /// </summary>
    public void ShowModel()
    {
        RequestModelRender();
    }

    public void OnPointerEnter(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: " + this);    
        OnSlotHovered.Invoke(this, true);
    }

    public void OnPointerExit(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        Debug.Log("Hovering slot: 2 " + this);
        OnSlotHovered.Invoke(this, false);
    }

    public void OnPointerClick(UnityEngine.EventSystems.PointerEventData pointerEventData)
    {
        if (OnSlotPressed != null) OnSlotPressed(this);
    }

    /// <summary>
    /// Clears the slot's visual representation, making it appear empty.
    /// </summary>
    public void Clear()
    {
        itemSO = null;
        itemIcon.enabled = false;
        itemIcon.sprite = null;
        itemIconModel.enabled = false;
        itemIconModel.texture = null;
        amountText.enabled = false;
        amountText.text = "";
        texture = null;
        isRenderingRequested = false;
    }
}