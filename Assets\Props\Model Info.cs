using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using System;
using UnityEditor;

[System.Serializable]
public class ModelInfo
{
    public GameObject prefab; // Prefab do modelo
    public Vector3 defaultRotation; // Rotação padrão do modelo
    public Vector3 intrinsicForward; // Direção intrínseca do modelo
    public Vector3 centerOffset; // Deslocamento do centro do modelo

    public Vector3 scaleRatio = Vector3.one; // Razão de escala para cada eixo
    [Min(0.01f)] public float meshBoundsRadius = 1f; 
    public bool useAutoBounds = true;    // Usar limites automáticos ou personalizados
}
