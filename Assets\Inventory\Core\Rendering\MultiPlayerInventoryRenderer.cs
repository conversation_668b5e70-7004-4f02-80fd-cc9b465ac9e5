using UnityEngine;
using UnityEngine.Rendering;
using System.Collections.Generic;
using System.Collections;
using System.Linq;

/// <summary>
/// Multi-player aware inventory rendering system that creates separate rendering areas
/// for each player to avoid conflicts and ensure proper isolation.
/// </summary>
public class MultiPlayerInventoryRenderer : MonoBehaviour, IInventoryRenderer
{
    [Header("Multi-Player Configuration")]
    [SerializeField] private int maxSupportedPlayers = 4;
    [SerializeField] private float playerAreaSeparation = 2000f; // Distance between player render areas
    [SerializeField] private Vector3 baseRenderPosition = new Vector3(10000, 10000, 10000);
    
    [Header("Rendering Configuration")]
    [SerializeField] private int renderTextureSize = 256;
    [SerializeField] private RenderTextureFormat renderTextureFormat = RenderTextureFormat.ARGB32;
    [SerializeField] private int renderTextureDepth = 24;
    
    [Header("Performance Settings")]
    [SerializeField] private int maxRendersPerFramePerPlayer = 2;
    [SerializeField] private int maxTotalRendersPerFrame = 6;
    
    // Singleton instance
    private static MultiPlayerInventoryRenderer _instance;
    public static MultiPlayerInventoryRenderer Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = FindObjectOfType<MultiPlayerInventoryRenderer>();
                if (_instance == null)
                {
                    GameObject go = new GameObject("MultiPlayerInventoryRenderer");
                    _instance = go.AddComponent<MultiPlayerInventoryRenderer>();
                    DontDestroyOnLoad(go);
                }
            }
            return _instance;
        }
    }
    
    // Per-player rendering data
    private Dictionary<string, PlayerRenderData> playerRenderData = new Dictionary<string, PlayerRenderData>();
    private InventoryRenderCache sharedCache; // Shared cache for efficiency
    private int totalRendersThisFrame = 0;
    
    private struct PlayerRenderData
    {
        public string playerID;
        public Vector3 renderAreaPosition;
        public Camera renderCamera;
        public Transform renderArea;
        public Transform modelContainer;
        public Light renderLight;
        public Queue<RenderRequest> renderQueue;
        public Queue<RenderRequest> priorityQueue;
        public bool isRendering;
        public int rendersThisFrame;
        
        public PlayerRenderData(string id, Vector3 position)
        {
            playerID = id;
            renderAreaPosition = position;
            renderCamera = null;
            renderArea = null;
            modelContainer = null;
            renderLight = null;
            renderQueue = new Queue<RenderRequest>();
            priorityQueue = new Queue<RenderRequest>();
            isRendering = false;
            rendersThisFrame = 0;
        }
    }
    
    public enum RenderPriority
    {
        Normal,
        High
    }
    
    private struct RenderRequest
    {
        public ItemSO itemSO;
        public System.Action<RenderTexture> onComplete;
        public bool useCache;
        public RenderPriority priority;
        public float requestTime;
        public string playerID;
        
        public RenderRequest(ItemSO item, System.Action<RenderTexture> callback, string playerId, 
                           bool cache = true, RenderPriority priority = RenderPriority.Normal)
        {
            itemSO = item;
            onComplete = callback;
            playerID = playerId;
            useCache = cache;
            this.priority = priority;
            requestTime = Time.time;
        }
    }
    
    void Awake()
    {
        if (_instance == null)
        {
            _instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeSystem();
        }
        else if (_instance != this)
        {
            Destroy(gameObject);
        }
    }
    
    void Start()
    {
        // Register for player events
        if (PlayersManager.Instance != null)
        {
            PlayersManager.Instance.OnUserJoined += OnPlayerJoined;
            PlayersManager.Instance.OnUserLeft += OnPlayerLeft;
            
            // Initialize existing players
            foreach (var user in PlayersManager.Instance.ActiveUsers)
            {
                OnPlayerJoined(user);
            }
        }
    }
    
    void Update()
    {
        // Reset frame counters
        totalRendersThisFrame = 0;
        foreach (var kvp in playerRenderData)
        {
            var data = kvp.Value;
            data.rendersThisFrame = 0;
            playerRenderData[kvp.Key] = data;
        }
        
        // Process render queues for all players
        ProcessAllPlayerQueues();
    }
    
    private void InitializeSystem()
    {
        // Initialize shared cache
        sharedCache = new InventoryRenderCache(
            maxSize: 200, // Larger cache for multiple players
            expirationTime: 600f, // Longer expiration for multi-player
            persistent: false
        );
        
        Debug.Log("MultiPlayerInventoryRenderer initialized");
    }
    
    private void OnPlayerJoined(UserMonoBehaviour user)
    {
        if (user == null) return;
        
        string playerID = user.UserID;
        if (playerRenderData.ContainsKey(playerID))
        {
            Debug.LogWarning($"Player {playerID} already has render data");
            return;
        }
        
        // Calculate isolated position for this player
        Vector3 playerPosition = CalculatePlayerRenderPosition(user.PlayerIndex);
        
        // Create render data for this player
        PlayerRenderData data = new PlayerRenderData(playerID, playerPosition);
        playerRenderData[playerID] = data;
        
        // Initialize rendering components for this player
        InitializePlayerRenderComponents(playerID);
        
        Debug.Log($"Initialized render area for player {playerID} at position {playerPosition}");
    }
    
    private void OnPlayerLeft(UserMonoBehaviour user)
    {
        if (user == null) return;
        
        string playerID = user.UserID;
        if (playerRenderData.TryGetValue(playerID, out PlayerRenderData data))
        {
            // Clean up player's render components
            CleanupPlayerRenderComponents(data);
            playerRenderData.Remove(playerID);
            
            Debug.Log($"Cleaned up render area for player {playerID}");
        }
    }
    
    private Vector3 CalculatePlayerRenderPosition(int playerIndex)
    {
        // Create isolated positions for each player
        // Spread them out in different directions to ensure complete isolation
        Vector3[] directions = {
            Vector3.right,      // Player 0: +X
            Vector3.left,       // Player 1: -X  
            Vector3.forward,    // Player 2: +Z
            Vector3.back        // Player 3: -Z
        };
        
        int directionIndex = playerIndex % directions.Length;
        Vector3 direction = directions[directionIndex];
        
        // Add vertical separation for additional players
        float verticalOffset = (playerIndex / directions.Length) * playerAreaSeparation;
        
        return baseRenderPosition + (direction * playerAreaSeparation) + (Vector3.up * verticalOffset);
    }
    
    private void InitializePlayerRenderComponents(string playerID)
    {
        if (!playerRenderData.TryGetValue(playerID, out PlayerRenderData data))
            return;
        
        // Create render area
        GameObject renderAreaGO = new GameObject($"RenderArea_Player_{playerID}");
        renderAreaGO.transform.position = data.renderAreaPosition;
        renderAreaGO.transform.SetParent(transform);
        data.renderArea = renderAreaGO.transform;
        
        // Create model container
        GameObject containerGO = new GameObject($"ModelContainer_Player_{playerID}");
        containerGO.transform.SetParent(data.renderArea);
        containerGO.transform.localPosition = Vector3.zero;
        data.modelContainer = containerGO.transform;
        
        // Create camera
        GameObject cameraGO = new GameObject($"RenderCamera_Player_{playerID}");
        cameraGO.transform.SetParent(data.renderArea);
        cameraGO.transform.localPosition = Vector3.back * 5f;
        
        data.renderCamera = cameraGO.AddComponent<Camera>();
        data.renderCamera.enabled = false;
        data.renderCamera.clearFlags = CameraClearFlags.SolidColor;
        data.renderCamera.backgroundColor = Color.clear;
        data.renderCamera.fieldOfView = 60f;
        data.renderCamera.nearClipPlane = 0.1f;
        data.renderCamera.farClipPlane = 100f;
        
        // Create lighting
        GameObject lightGO = new GameObject($"RenderLight_Player_{playerID}");
        lightGO.transform.SetParent(data.renderArea);
        lightGO.transform.localPosition = Vector3.zero;
        lightGO.transform.localRotation = Quaternion.Euler(50f, -30f, 0f);
        
        data.renderLight = lightGO.AddComponent<Light>();
        data.renderLight.type = LightType.Directional;
        data.renderLight.color = Color.white;
        data.renderLight.intensity = 1.2f;
        data.renderLight.enabled = false;
        
        // Update the data
        playerRenderData[playerID] = data;
    }
    
    private void CleanupPlayerRenderComponents(PlayerRenderData data)
    {
        if (data.renderArea != null)
        {
            DestroyImmediate(data.renderArea.gameObject);
        }
    }
    
    /// <summary>
    /// Requests rendering for a specific player's inventory item.
    /// </summary>
    public void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, 
                             string playerID, bool useCache = true, RenderPriority priority = RenderPriority.Normal)
    {
        if (itemSO == null)
        {
            onComplete?.Invoke(null);
            return;
        }
        
        // Check cache first if enabled
        if (useCache && sharedCache.Contains(itemSO))
        {
            RenderTexture cachedTexture = sharedCache.Get(itemSO);
            if (cachedTexture != null)
            {
                onComplete?.Invoke(cachedTexture);
                return;
            }
        }
        
        // Ensure player has render data
        if (!playerRenderData.ContainsKey(playerID))
        {
            Debug.LogWarning($"No render data for player {playerID}");
            onComplete?.Invoke(null);
            return;
        }
        
        // Create render request
        RenderRequest request = new RenderRequest(itemSO, onComplete, playerID, useCache, priority);
        
        // Add to appropriate queue
        var data = playerRenderData[playerID];
        if (priority == RenderPriority.High)
        {
            data.priorityQueue.Enqueue(request);
        }
        else
        {
            data.renderQueue.Enqueue(request);
        }
        playerRenderData[playerID] = data;
    }
    
    // Legacy compatibility method
    public void RequestRender(ItemSO itemSO, System.Action<RenderTexture> onComplete, 
                             bool useCache = true, RenderPriority priority = RenderPriority.Normal)
    {
        // Try to determine player ID from current context
        string playerID = GetCurrentPlayerID();
        RequestRender(itemSO, onComplete, playerID, useCache, priority);
    }
    
    private string GetCurrentPlayerID()
    {
        // Try to get the main user's ID as fallback
        if (PlayersManager.Instance?.MainUser != null)
        {
            return PlayersManager.Instance.MainUser.UserID;
        }
        
        // If no main user, use the first available player
        if (playerRenderData.Count > 0)
        {
            return playerRenderData.Keys.First();
        }
        
        return "default";
    }
    
    private void ProcessAllPlayerQueues()
    {
        foreach (var kvp in playerRenderData.ToList())
        {
            if (totalRendersThisFrame >= maxTotalRendersPerFrame)
                break;
                
            ProcessPlayerQueue(kvp.Key);
        }
    }
    
    private void ProcessPlayerQueue(string playerID)
    {
        if (!playerRenderData.TryGetValue(playerID, out PlayerRenderData data))
            return;
            
        if (data.isRendering || data.rendersThisFrame >= maxRendersPerFramePerPlayer)
            return;
            
        // Process priority queue first
        if (data.priorityQueue.Count > 0)
        {
            RenderRequest request = data.priorityQueue.Dequeue();
            StartCoroutine(ProcessRenderRequest(request, data));
        }
        else if (data.renderQueue.Count > 0)
        {
            RenderRequest request = data.renderQueue.Dequeue();
            StartCoroutine(ProcessRenderRequest(request, data));
        }
    }
    
    private IEnumerator ProcessRenderRequest(RenderRequest request, PlayerRenderData data)
    {
        data.isRendering = true;
        data.rendersThisFrame++;
        totalRendersThisFrame++;
        playerRenderData[request.playerID] = data;
        
        // Render the item using the player's dedicated render area
        RenderTexture result = yield return StartCoroutine(RenderItemForPlayer(request, data));
        
        // Cache the result if requested
        if (request.useCache && result != null)
        {
            sharedCache.Add(request.itemSO, result, request.priority == RenderPriority.High);
        }
        
        // Complete the request
        request.onComplete?.Invoke(result);
        
        data.isRendering = false;
        playerRenderData[request.playerID] = data;
    }
    
    private IEnumerator RenderItemForPlayer(RenderRequest request, PlayerRenderData data)
    {
        // Implementation similar to original renderer but using player-specific components
        // This would contain the actual rendering logic using data.renderCamera, data.modelContainer, etc.
        
        // For now, return a placeholder
        yield return null;
        
        // Create render texture
        RenderTexture renderTexture = new RenderTexture(renderTextureSize, renderTextureSize, renderTextureDepth, renderTextureFormat);
        
        // TODO: Implement actual rendering logic here
        
        return renderTexture;
    }
    
    // Interface implementations and utility methods
    public RenderTexture GetCachedTexture(ItemSO itemSO) => sharedCache?.Get(itemSO);
    public bool HasCachedTexture(ItemSO itemSO) => sharedCache?.Contains(itemSO) ?? false;
    public void ClearCache() => sharedCache?.Clear();
    public bool IsRendering() => playerRenderData.Values.Any(data => data.isRendering);
    
    public int GetQueueSize() => playerRenderData.Values.Sum(data => data.renderQueue.Count + data.priorityQueue.Count);
    public int GetPlayerQueueSize(string playerID) => 
        playerRenderData.TryGetValue(playerID, out var data) ? data.renderQueue.Count + data.priorityQueue.Count : 0;
    
    void OnDestroy()
    {
        // Cleanup all player render areas
        foreach (var data in playerRenderData.Values)
        {
            CleanupPlayerRenderComponents(data);
        }
        
        ClearCache();
        
        // Unregister events
        if (PlayersManager.Instance != null)
        {
            PlayersManager.Instance.OnUserJoined -= OnPlayerJoined;
            PlayersManager.Instance.OnUserLeft -= OnPlayerLeft;
        }
    }
}
