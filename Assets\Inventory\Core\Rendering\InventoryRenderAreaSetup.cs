using UnityEngine;

/// <summary>
/// Utility class for setting up and configuring the isolated inventory rendering area.
/// Provides methods for positioning the render area away from the game world and configuring lighting.
/// </summary>
public static class InventoryRenderAreaSetup
{
    /// <summary>
    /// Default positions for the isolated render area, far from typical game world coordinates.
    /// </summary>
    public static readonly Vector3[] DefaultRenderPositions = new Vector3[]
    {
        new Vector3(10000, 10000, 10000),   // Primary position
        new Vector3(-10000, 10000, 10000),  // Alternative position 1
        new Vector3(10000, -10000, 10000),  // Alternative position 2
        new Vector3(10000, 10000, -10000),  // Alternative position 3
    };
    
    /// <summary>
    /// Finds an optimal position for the render area that doesn't conflict with existing game objects.
    /// </summary>
    /// <param name="searchRadius">Radius to check for conflicts around each position</param>
    /// <returns>An optimal position for the render area</returns>
    public static Vector3 FindOptimalRenderPosition(float searchRadius = 100f)
    {
        foreach (Vector3 position in DefaultRenderPositions)
        {
            if (IsPositionClear(position, searchRadius))
            {
                return position;
            }
        }
        
        // If all default positions are occupied, generate a random far position
        return GenerateRandomFarPosition();
    }
    
    /// <summary>
    /// Checks if a position is clear of other game objects within a given radius.
    /// </summary>
    /// <param name="position">Position to check</param>
    /// <param name="radius">Radius to check around the position</param>
    /// <returns>True if the position is clear</returns>
    private static bool IsPositionClear(Vector3 position, float radius)
    {
        Collider[] colliders = Physics.OverlapSphere(position, radius);
        
        // Filter out colliders that belong to the inventory render system
        foreach (Collider col in colliders)
        {
            if (col.transform.root.name != "InventoryModelRenderer")
            {
                return false; // Found a conflicting object
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// Generates a random position far from the origin for the render area.
    /// </summary>
    /// <returns>A random far position</returns>
    private static Vector3 GenerateRandomFarPosition()
    {
        float distance = Random.Range(8000f, 15000f);
        Vector3 direction = Random.onUnitSphere;
        return direction * distance;
    }
    
    /// <summary>
    /// Creates and configures optimal lighting for inventory item rendering.
    /// </summary>
    /// <param name="renderArea">The render area transform to attach lighting to</param>
    /// <returns>The configured light component</returns>
    public static Light SetupOptimalLighting(Transform renderArea)
    {
        GameObject lightGO = new GameObject("InventoryRenderLight");
        lightGO.transform.SetParent(renderArea);
        
        Light light = lightGO.AddComponent<Light>();
        
        // Configure for optimal item visibility
        light.type = LightType.Directional;
        light.color = Color.white;
        light.intensity = 1.2f;
        light.shadows = LightShadows.None; // Disable shadows for performance
        
        // Position light for good item illumination
        light.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
        
        return light;
    }
    
    /// <summary>
    /// Sets up additional fill lighting to reduce harsh shadows on items.
    /// </summary>
    /// <param name="renderArea">The render area transform to attach lighting to</param>
    /// <param name="mainLight">The main directional light</param>
    /// <returns>The fill light component</returns>
    public static Light SetupFillLighting(Transform renderArea, Light mainLight)
    {
        GameObject fillLightGO = new GameObject("InventoryFillLight");
        fillLightGO.transform.SetParent(renderArea);
        
        Light fillLight = fillLightGO.AddComponent<Light>();
        
        // Configure as softer fill light
        fillLight.type = LightType.Directional;
        fillLight.color = new Color(0.8f, 0.9f, 1f, 1f); // Slightly blue tint
        fillLight.intensity = 0.3f;
        fillLight.shadows = LightShadows.None;
        
        // Position opposite to main light for fill
        Vector3 mainRotation = mainLight.transform.eulerAngles;
        fillLight.transform.rotation = Quaternion.Euler(
            -mainRotation.x + 180f, 
            mainRotation.y + 180f, 
            mainRotation.z
        );
        
        return fillLight;
    }
    
    /// <summary>
    /// Creates a background setup for the render area to ensure clean item renders.
    /// </summary>
    /// <param name="renderArea">The render area transform</param>
    /// <param name="backgroundColor">Background color for the render area</param>
    public static void SetupRenderBackground(Transform renderArea, Color backgroundColor = default)
    {
        if (backgroundColor == default)
        {
            backgroundColor = Color.clear; // Transparent by default
        }
        
        // Create a background plane if needed for specific effects
        // This is optional and mainly for debugging or special visual effects
        GameObject backgroundGO = new GameObject("RenderBackground");
        backgroundGO.transform.SetParent(renderArea);
        backgroundGO.transform.localPosition = new Vector3(0, 0, 10f); // Behind items
        
        // Add a simple quad for background if needed
        MeshRenderer renderer = backgroundGO.AddComponent<MeshRenderer>();
        MeshFilter filter = backgroundGO.AddComponent<MeshFilter>();
        
        // Create a simple quad mesh
        filter.mesh = CreateQuadMesh();
        
        // Create a simple material
        Material backgroundMat = new Material(Shader.Find("Unlit/Color"));
        backgroundMat.color = backgroundColor;
        renderer.material = backgroundMat;
        
        // Scale to cover camera view
        backgroundGO.transform.localScale = Vector3.one * 20f;
        
        // Disable by default (only enable if needed)
        backgroundGO.SetActive(false);
    }
    
    /// <summary>
    /// Creates a simple quad mesh for background rendering.
    /// </summary>
    /// <returns>A quad mesh</returns>
    private static Mesh CreateQuadMesh()
    {
        Mesh mesh = new Mesh();
        mesh.name = "RenderBackgroundQuad";
        
        Vector3[] vertices = new Vector3[]
        {
            new Vector3(-1, -1, 0),
            new Vector3(1, -1, 0),
            new Vector3(1, 1, 0),
            new Vector3(-1, 1, 0)
        };
        
        int[] triangles = new int[]
        {
            0, 2, 1,
            0, 3, 2
        };
        
        Vector2[] uvs = new Vector2[]
        {
            new Vector2(0, 0),
            new Vector2(1, 0),
            new Vector2(1, 1),
            new Vector2(0, 1)
        };
        
        mesh.vertices = vertices;
        mesh.triangles = triangles;
        mesh.uv = uvs;
        mesh.RecalculateNormals();
        
        return mesh;
    }
    
    /// <summary>
    /// Validates that the render area is properly isolated from the game world.
    /// </summary>
    /// <param name="renderPosition">Position of the render area</param>
    /// <param name="gameWorldBounds">Bounds of the main game world</param>
    /// <returns>True if the render area is properly isolated</returns>
    public static bool ValidateRenderAreaIsolation(Vector3 renderPosition, Bounds gameWorldBounds)
    {
        float distance = Vector3.Distance(renderPosition, gameWorldBounds.center);
        float minSafeDistance = gameWorldBounds.size.magnitude + 1000f; // Add safety margin
        
        return distance > minSafeDistance;
    }
    
    /// <summary>
    /// Gets recommended camera settings for inventory item rendering.
    /// </summary>
    /// <returns>Camera configuration data</returns>
    public static CameraConfig GetRecommendedCameraConfig()
    {
        return new CameraConfig
        {
            fieldOfView = 60f,
            nearClipPlane = 0.1f,
            farClipPlane = 100f,
            clearFlags = CameraClearFlags.SolidColor,
            backgroundColor = Color.clear,
            cullingMask = LayerMask.GetMask("Default"),
            renderingPath = RenderingPath.Forward
        };
    }
    
    [System.Serializable]
    public struct CameraConfig
    {
        public float fieldOfView;
        public float nearClipPlane;
        public float farClipPlane;
        public CameraClearFlags clearFlags;
        public Color backgroundColor;
        public LayerMask cullingMask;
        public RenderingPath renderingPath;
    }

}
