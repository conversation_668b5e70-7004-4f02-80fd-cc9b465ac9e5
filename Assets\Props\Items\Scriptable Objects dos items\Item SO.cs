using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using UnityEngine.UI;
using UnityEditor;

[System.Serializable]
[CreateAssetMenu(fileName = "NewItem", menuName = "Inventory/Items/Item SO")]
public class ItemSO : ScriptableObject
{
    public int ID; // ID do item
    public string itemName; // Nome do item
    public string description; // Descrição do item
    public Sprite icon;
    public ModelInfo modelInfo; // Informações do modelo 3D

    public enum StackType { None, Stackable, UnStackable };
    public StackType stack;
    public int maxStack; // Stack Maximo
    public float value;
    [Min(1)] public int weight;

    // Novas propriedades para o tamanho na grade
    [Min(1)] public int gridWidth = 1;  // Largura que o item ocupa na grade (padrão 1)
    [Min(1)] public int gridHeight = 1; // Altura que o item ocupa na grade (padrão 1)

    public GameObject GetPrefab()
    {
        return modelInfo.prefab != null ? modelInfo.prefab : null;
    }

    public Vector3 GetCenterOffset()
    {
        return modelInfo.centerOffset != Vector3.zero ? modelInfo.centerOffset : Vector3.zero;
    }

    public Vector3 GetDefaultRotation()
    {
        return modelInfo.defaultRotation != Vector3.zero ? modelInfo.defaultRotation : Vector3.zero;
    }

    public Vector3 GetIntrinsicForward()
    {
        return modelInfo.intrinsicForward != Vector3.zero ? modelInfo.intrinsicForward : Vector3.forward;
    }

    // Métodos auxiliares para empilhamento
    public bool IsStackable() => stack == StackType.Stackable;
    public bool IsUnStackable() => stack == StackType.UnStackable;
    
    public bool canBeRotated = false; // Valor padrão é false
    public int GetRotatedWidth(bool rotated) { return rotated ? gridHeight : gridWidth; }
    public int GetRotatedHeight(bool rotated) { return rotated ? gridWidth : gridHeight; }

    public GameObject prefab => modelInfo.prefab;
    public Vector3 centerOffset => modelInfo.centerOffset;
    public Vector3 defaultRotation => modelInfo.defaultRotation;
    public Vector3 intrinsicForward => modelInfo.intrinsicForward;

    public Vector3 scaleRatio => modelInfo.scaleRatio;
    public float meshBoundsRadius => modelInfo.meshBoundsRadius; // Alterado para float
    public bool useAutoBounds => modelInfo.useAutoBounds;
}


[CustomEditor(typeof(ItemSO), true)]
public class ItemEditor : Editor
{
    private bool showBasicInfo = true;
    private bool showStackingInfo = true;
    private bool showGridInfo = true;
    private bool showModelInfo = true;

    // Preview
    private Editor gameObjectEditor;
    private bool showModelPreview = false;

    public override void OnInspectorGUI()
    {
        ItemSO item = (ItemSO)target;
        serializedObject.Update();

        // Header with item name
        EditorGUILayout.Space();
        GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
        headerStyle.fontSize = 16;
        headerStyle.alignment = TextAnchor.MiddleCenter;

        string displayName = string.IsNullOrEmpty(item.itemName) ? "Unnamed Item" : item.itemName;
        EditorGUILayout.LabelField(displayName, headerStyle);
        EditorGUILayout.Space();

        // Basic Information Section
        EditorGUILayout.BeginVertical("box");
        showBasicInfo = EditorGUILayout.Foldout(showBasicInfo, "Basic Information", true, EditorStyles.foldoutHeader);
        if (showBasicInfo)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.PropertyField(serializedObject.FindProperty("ID"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("itemName"));

            // Description with text area
            EditorGUILayout.LabelField("Description:");
            var descProp = serializedObject.FindProperty("description");
            descProp.stringValue = EditorGUILayout.TextArea(descProp.stringValue, GUILayout.MinHeight(60));

            EditorGUILayout.PropertyField(serializedObject.FindProperty("icon"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("weight"));

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();

        // Stacking Information Section
        EditorGUILayout.BeginVertical("box");
        showStackingInfo = EditorGUILayout.Foldout(showStackingInfo, "Stacking Properties", true, EditorStyles.foldoutHeader);
        if (showStackingInfo)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.PropertyField(serializedObject.FindProperty("stack"));

            if (item.stack == ItemSO.StackType.Stackable)
            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty("maxStack"));

                // Visual feedback for stacking
                EditorGUILayout.Space();
                EditorGUILayout.HelpBox($"This item can stack up to {item.maxStack} units per slot.", MessageType.Info);
            }
            else if (item.stack == ItemSO.StackType.UnStackable)
            {
                EditorGUILayout.HelpBox("This item cannot be stacked.", MessageType.Info);
            }
            else
            {
                EditorGUILayout.HelpBox("Stacking behavior not defined.", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();

        // Grid Information Section
        EditorGUILayout.BeginVertical("box");
        showGridInfo = EditorGUILayout.Foldout(showGridInfo, "Grid Properties", true, EditorStyles.foldoutHeader);
        if (showGridInfo)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PropertyField(serializedObject.FindProperty("gridWidth"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("gridHeight"));
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.PropertyField(serializedObject.FindProperty("canBeRotated"));

            // Visual grid representation
            EditorGUILayout.Space();
            DrawGridVisualization(item);

            // Grid info summary
            EditorGUILayout.Space();
            int totalCells = item.gridWidth * item.gridHeight;
            string rotationText = item.canBeRotated ? $" (or {item.gridHeight}x{item.gridWidth} when rotated)" : "";
            EditorGUILayout.HelpBox($"Occupies {totalCells} grid cell(s) in a {item.gridWidth}x{item.gridHeight} pattern{rotationText}.", MessageType.Info);

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();

        // Model Information Section
        EditorGUILayout.BeginVertical("box");
        showModelInfo = EditorGUILayout.Foldout(showModelInfo, "3D Model Properties", true, EditorStyles.foldoutHeader);
        if (showModelInfo)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.prefab"));

            // Center Offset with auto-calculation tools
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.centerOffset"));
            if (GUILayout.Button("Auto", GUILayout.Width(50)))
            {
                AutoCalculateCenterOffsetInInspector(item);
            }
            if (GUILayout.Button("Zero", GUILayout.Width(50)))
            {
                ResetCenterOffsetInInspector(item);
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.defaultRotation"));
            EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.intrinsicForward"));
            
            // Novos campos para visualização
            EditorGUILayout.Space();
            EditorGUILayout.LabelField("Visualization Settings:", EditorStyles.boldLabel);
            
            SerializedProperty useAutoBoundsProp = serializedObject.FindProperty("modelInfo.useAutoBounds");
            EditorGUILayout.PropertyField(useAutoBoundsProp, new GUIContent("Auto Bounds", "Use automatic bounds calculation"));

            if (!useAutoBoundsProp.boolValue)
            {
                EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.meshBoundsRadius"), 
                    new GUIContent("Mesh Bounds Radius", "Custom radius of the mesh bounds sphere"));
            }
            
            EditorGUILayout.PropertyField(serializedObject.FindProperty("modelInfo.scaleRatio"), 
                new GUIContent("View Scale Ratio", "Scale proportion for model visualization"));
            
            // Model preview button
            EditorGUILayout.Space();
            if (GUILayout.Button("Open Advanced Model Preview", GUILayout.Height(30)))
            {
                ItemModelPreviewWindow.OpenWindow(item as ItemSO);
            }

            // Quick model info
            if (item.GetPrefab() != null)
            {
                EditorGUILayout.Space();
                EditorGUILayout.LabelField("Model Info:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Prefab: {item.GetPrefab().name}");
                EditorGUILayout.LabelField($"Center Offset: {item.GetCenterOffset()}");
                EditorGUILayout.LabelField($"Default Rotation: {item.GetDefaultRotation()}");
                EditorGUILayout.LabelField($"Intrinsic Forward: {item.GetIntrinsicForward()}");
                EditorGUILayout.LabelField($"View Scale Ratio: {item.modelInfo.scaleRatio}");

                if (!item.modelInfo.useAutoBounds)
                {
                    EditorGUILayout.LabelField($"Custom Bounds Radius: {item.modelInfo.meshBoundsRadius}");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No prefab assigned for 3D model preview.", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
        }

        EditorGUILayout.EndVertical();

        // Summary Section
        EditorGUILayout.Space();
        DrawItemSummary(item);

        serializedObject.ApplyModifiedProperties();

        // Mark dirty if changed
        if (GUI.changed)
        {
            EditorUtility.SetDirty(item);
        }
    }

    void DrawGridVisualization(ItemSO item)
    {
        EditorGUILayout.LabelField("Grid Visualization:", EditorStyles.boldLabel);

        float cellSize = 20f;
        Color occupiedColor = new Color(0.2f, 0.8f, 0.2f, 0.8f);
        Color emptyColor = new Color(0.8f, 0.8f, 0.8f, 0.3f);

        // Draw normal orientation
        EditorGUILayout.LabelField($"Normal ({item.gridWidth}x{item.gridHeight}):");
        DrawGrid(item.gridWidth, item.gridHeight, cellSize, occupiedColor, emptyColor);

        // Draw rotated orientation if applicable
        if (item.canBeRotated && (item.gridWidth != item.gridHeight))
        {
            EditorGUILayout.Space();
            EditorGUILayout.LabelField($"Rotated ({item.gridHeight}x{item.gridWidth}):");
            DrawGrid(item.gridHeight, item.gridWidth, cellSize, occupiedColor, emptyColor);
        }
    }

    void DrawGrid(int width, int height, float cellSize, Color occupiedColor, Color emptyColor)
    {
        for (int y = 0; y < height; y++)
        {
            EditorGUILayout.BeginHorizontal();
            for (int x = 0; x < width; x++)
            {
                GUI.backgroundColor = occupiedColor;
                GUILayout.Button("", GUILayout.Width(cellSize), GUILayout.Height(cellSize));
            }
            EditorGUILayout.EndHorizontal();
        }
        GUI.backgroundColor = Color.white;
    }

    void DrawItemSummary(ItemSO item)
    {
        EditorGUILayout.LabelField("Item Summary", EditorStyles.boldLabel);
        EditorGUILayout.BeginVertical("box");

        string stackText = item.stack == ItemSO.StackType.Stackable ? $"Stackable (max {item.maxStack})" :
                          item.stack == ItemSO.StackType.UnStackable ? "Non-stackable" : "Undefined";

        string gridText = $"{item.gridWidth}x{item.gridHeight} ({item.gridWidth * item.gridHeight} cells)";
        if (item.canBeRotated) gridText += " - Rotatable";

        string modelText = item.GetPrefab() != null ? item.GetPrefab().name : "No prefab";

        EditorGUILayout.LabelField($"ID: {item.ID} | Weight: {item.weight}");
        EditorGUILayout.LabelField($"Stacking: {stackText}");
        EditorGUILayout.LabelField($"Grid: {gridText}");
        EditorGUILayout.LabelField($"Model: {modelText}");

        EditorGUILayout.EndVertical();
    }

    /// <summary>
    /// Auto-calculates center offset for use in the main inspector
    /// </summary>
    void AutoCalculateCenterOffsetInInspector(ItemSO item)
    {
        if (item?.GetPrefab() == null)
        {
            Debug.LogWarning("Cannot auto-calculate center offset: No prefab assigned.");
            return;
        }

        Vector3 calculatedCenter = CalculateAccurateMeshCenterStatic(item.GetPrefab());

        if (calculatedCenter == Vector3.positiveInfinity)
        {
            Debug.LogWarning("Cannot auto-calculate center offset: No valid mesh data found.");
            return;
        }

        Undo.RecordObject(item, "Auto-calculate Model Center Offset");
        item.modelInfo.centerOffset = calculatedCenter;
        EditorUtility.SetDirty(item);
        Debug.Log($"Auto-calculated precise center offset for '{item.itemName}': {calculatedCenter}");
    }

    /// <summary>
    /// Resets center offset to zero for use in the main inspector
    /// </summary>
    void ResetCenterOffsetInInspector(ItemSO item)
    {
        if (item == null) return;

        Undo.RecordObject(item, "Reset Model Center Offset");
        item.modelInfo.centerOffset = Vector3.zero;
        EditorUtility.SetDirty(item);
        Debug.Log($"Reset center offset for '{item.itemName}' to zero.");
    }

    /// <summary>
    /// Static version of the accurate mesh center calculation for use in the main inspector
    /// </summary>
    static Vector3 CalculateAccurateMeshCenterStatic(GameObject prefab)
    {
        // Get all MeshFilter components in the prefab and its children
        MeshFilter[] filters = prefab.GetComponentsInChildren<MeshFilter>(true);

        // Also check for SkinnedMeshRenderer components for animated meshes
        SkinnedMeshRenderer[] skinnedRenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);

        if (filters.Length == 0 && skinnedRenderers.Length == 0)
        {
            return Vector3.positiveInfinity;
        }

        Vector3 min = Vector3.positiveInfinity;
        Vector3 max = Vector3.negativeInfinity;

        // Process regular MeshFilter components
        foreach (var filter in filters)
        {
            if (filter.sharedMesh == null) continue;

            Vector3[] vertices = filter.sharedMesh.vertices;
            Transform meshTransform = filter.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 prefabLocalPoint = prefab.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, prefabLocalPoint);
                max = Vector3.Max(max, prefabLocalPoint);
            }
        }

        // Process SkinnedMeshRenderer components
        foreach (var skinnedRenderer in skinnedRenderers)
        {
            if (skinnedRenderer.sharedMesh == null) continue;

            Vector3[] vertices = skinnedRenderer.sharedMesh.vertices;
            Transform meshTransform = skinnedRenderer.transform;

            foreach (Vector3 vertex in vertices)
            {
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 prefabLocalPoint = prefab.transform.InverseTransformPoint(worldPoint);

                min = Vector3.Min(min, prefabLocalPoint);
                max = Vector3.Max(max, prefabLocalPoint);
            }
        }

        if (min == Vector3.positiveInfinity || max == Vector3.negativeInfinity)
        {
            return Vector3.positiveInfinity;
        }

        return (min + max) * 0.5f;
    }
}

public class ItemModelPreviewWindow : EditorWindow
{
    private ItemSO currentItem;
    private Editor itemEditor;
    private PreviewRenderUtility previewRenderUtility;
    private GameObject previewInstance;

    // Variáveis para controle da câmera do preview
    private Vector2 scrollPosition; // Para o scroll do inspetor
    private float cameraDistance = 2.0f;
    private Vector2 cameraOrbitAngles = new Vector2(20f, 0f); // Pitch, Yaw
    private GameObject lastPreviewedPrefab;

    // Nova variável para a cor da linha de direção
    public Color forwardIndicatorColor = Color.yellow; // Renamed from directionLineColor
    public float forwardIndicatorDistance = 1f;    // Renamed from directionLineLength
    public float forwardIndicatorGizmoSize = 0.1f;   // Tamanho do gizmo no final da linha (cubo ou esfera)

    // Novas variáveis para o indicador de centro
    public bool showCenterIndicator = true;
    public Color centerIndicatorColor = Color.cyan;
    public float centerIndicatorSize = 0.1f;

    public static void OpenWindow(ItemSO item)
    {
        ItemModelPreviewWindow window = GetWindow<ItemModelPreviewWindow>("Item Model Preview & Edit");
        window.SetItem(item);
        window.Show();
    }

    void SetItem(ItemSO newItem)
    {
        currentItem = newItem;
        if (currentItem != null)
        {
            itemEditor = Editor.CreateEditor(currentItem);
            lastPreviewedPrefab = null; // Força a recarga do prefab na próxima atualização
            InitializePreviewRenderUtility();
            SetupModelForPreview();
        }
        else
        {
            itemEditor = null;
            CleanUpPreviewModel();
        }
    }

    void OnEnable()
    {
        // Se currentItem foi serializado e é válido, reinicialize
        if (currentItem != null)
        {
            // Recria o editor, pois ele não é serializado bem por padrão
            itemEditor = Editor.CreateEditor(currentItem);
            InitializePreviewRenderUtility();
            SetupModelForPreview(); // Isso usará o currentItem.Prefab existente
        }
    }

    void InitializePreviewRenderUtility()
    {
        if (previewRenderUtility == null)
        {
            previewRenderUtility = new PreviewRenderUtility();
            previewRenderUtility.camera.fieldOfView = 30.0f;
            previewRenderUtility.camera.farClipPlane = 1000; // Aumentar o far clip plane

            // Adiciona uma luz padrão
            GameObject lightObj = new GameObject("PreviewLight_Window") { hideFlags = HideFlags.HideAndDontSave };
            Light lightComponent = lightObj.AddComponent<Light>();
            lightComponent.type = LightType.Directional;
            lightComponent.intensity = 1.5f; // Um pouco mais de intensidade
            lightComponent.transform.rotation = Quaternion.Euler(50f, 50f, 0);
            previewRenderUtility.AddSingleGO(lightObj);
        }
    }

    void CleanUpPreviewModel()
    {
        if (previewInstance != null)
        {
            DestroyImmediate(previewInstance);
            previewInstance = null;
        }
        lastPreviewedPrefab = null;
    }

    void SetupModelForPreview()
    {
        if (previewRenderUtility == null || currentItem == null)
        {
            CleanUpPreviewModel();
            return;
        }

        CleanUpPreviewModel(); // Limpa o modelo antigo primeiro

        if (currentItem.GetPrefab() != null)
        {
            previewInstance = previewRenderUtility.InstantiatePrefabInScene(currentItem.GetPrefab());
            if (previewInstance != null) // Verifica se a instanciação foi bem-sucedida
            {
                previewInstance.hideFlags = HideFlags.HideAndDontSave;
                // A posição é relativa à cena do PreviewRenderUtility, geralmente a origem.
                previewInstance.transform.position = Vector3.zero;
                previewInstance.transform.rotation = Quaternion.Euler(currentItem.GetDefaultRotation());
                Debug.Log($" scale ratio {currentItem.modelInfo.scaleRatio} transform scale { currentItem.prefab.transform.localScale}");
                currentItem.modelInfo.scaleRatio = currentItem.prefab.transform.localScale;

                Bounds bounds = CalculateBounds(previewInstance);
                if (bounds.size != Vector3.zero)
                {
                    float objectRadius = bounds.extents.magnitude > 0 ? bounds.extents.magnitude : bounds.size.magnitude;
                    float maxScaleComponent = Mathf.Max(currentItem.scaleRatio.x, currentItem.scaleRatio.y, currentItem.scaleRatio.z);
                    cameraDistance = objectRadius * 2.5f / Mathf.Sin(previewRenderUtility.camera.fieldOfView * 0.5f * Mathf.Deg2Rad) * maxScaleComponent;
                    cameraDistance = Mathf.Max(cameraDistance, 0.1f); // Garante uma distância mínima
                }
                else
                {
                    cameraDistance = 5.0f; // Distância padrão se não houver renderizadores ou tamanho zero
                }
            }
            lastPreviewedPrefab = currentItem.GetPrefab();
        }
    }

    Bounds CalculateBounds(GameObject go)
    {
        Bounds bounds = new Bounds(Vector3.zero, Vector3.zero);
        Renderer[] renderers = go.GetComponentsInChildren<Renderer>();
        if (renderers.Length > 0)
        {
            bounds = renderers[0].bounds;
            for (int i = 1; i < renderers.Length; i++)
            {
                if (renderers[i].enabled) // Considera apenas renderizadores ativos
                    bounds.Encapsulate(renderers[i].bounds);
            }
        }
        return bounds;
    }

    void OnDisable()
    {
        CleanUpPreviewModel();
        if (previewRenderUtility != null)
        {
            previewRenderUtility.Cleanup();
            previewRenderUtility = null;
        }
    }

    // Chamado 10 vezes por segundo por padrão, bom para checar mudanças no SO
    void OnInspectorUpdate()
    {
        if (currentItem == null || previewRenderUtility == null) return;

        bool needsRepaint = false;

        if (currentItem.GetPrefab() != lastPreviewedPrefab)
        {
            SetupModelForPreview();
            needsRepaint = true;
        }

        if (previewInstance != null &&
            previewInstance.transform.rotation.eulerAngles != currentItem.GetDefaultRotation())
        {
            previewInstance.transform.rotation = Quaternion.Euler(currentItem.GetDefaultRotation());
            needsRepaint = true;
        }

        // Check if scaleRatio changed and update previewInstance scale accordingly
        if (previewInstance != null && previewInstance.transform.localScale != currentItem.scaleRatio)
        {
            previewInstance.transform.localScale = currentItem.scaleRatio;
            needsRepaint = true;
        }

        if (needsRepaint)
        {
            Repaint();
        }
    }

    // UI State variables
    private bool showItemProperties = true;
    private bool showIndicatorSettings = true;
    private bool showPreviewControls = true;
    private bool showModelInfo = true;

    private void OnGUI()
    {
        if (currentItem == null)
        {
            DrawNoItemSelected();
            return;
        }

        if (itemEditor == null || itemEditor.target != currentItem)
            itemEditor = Editor.CreateEditor(currentItem);
        if (previewRenderUtility == null)
        {
            InitializePreviewRenderUtility();
            SetupModelForPreview();
        }

        // Main layout with sections
        DrawWindowHeader();

        // Scrollable content area
        scrollPosition = GUILayout.BeginScrollView(scrollPosition, GUILayout.Height(Mathf.Max(250, position.height * 0.45f)));

        DrawItemPropertiesSection();
        DrawModelInfoSection();
        DrawIndicatorSettingsSection();
        DrawPreviewControlsSection();

        GUILayout.EndScrollView();

        EditorGUILayout.Space(5);

        // Preview area (non-scrollable)
        DrawPreviewSection();
    }

    void DrawNoItemSelected()
    {
        EditorGUILayout.Space(20);
        EditorGUILayout.BeginVertical("box");

        GUIStyle centeredStyle = new GUIStyle(EditorStyles.largeLabel);
        centeredStyle.alignment = TextAnchor.MiddleCenter;
        centeredStyle.normal.textColor = Color.gray;

        EditorGUILayout.LabelField("No ItemSO Selected", centeredStyle);
        EditorGUILayout.Space();
        EditorGUILayout.LabelField("Select an ItemSO and click 'Open Advanced Model Preview' to use this window.", EditorStyles.centeredGreyMiniLabel);

        EditorGUILayout.EndVertical();

        if (itemEditor != null || previewInstance != null)
            SetItem(null); // Clean up if item became null
    }

    void DrawWindowHeader()
    {
        EditorGUILayout.BeginVertical("box");

        GUIStyle headerStyle = new GUIStyle(EditorStyles.boldLabel);
        headerStyle.fontSize = 14;
        headerStyle.alignment = TextAnchor.MiddleCenter;

        string itemName = string.IsNullOrEmpty(currentItem.itemName) ? "Unnamed Item" : currentItem.itemName;
        EditorGUILayout.LabelField($"Model Preview & Editor - {itemName}", headerStyle);

        if (currentItem.GetPrefab() != null)
        {
            EditorGUILayout.LabelField($"Prefab: {currentItem.GetPrefab().name}", EditorStyles.centeredGreyMiniLabel);
        }
        else
        {
            EditorGUILayout.LabelField("No prefab assigned", EditorStyles.centeredGreyMiniLabel);
        }

        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    void DrawItemPropertiesSection()
    {
        EditorGUILayout.BeginVertical("box");
        showItemProperties = EditorGUILayout.Foldout(showItemProperties, "Item Properties", true, EditorStyles.foldoutHeader);
        if (showItemProperties)
        {
            EditorGUILayout.BeginVertical("helpBox");

            if (itemEditor != null)
            {
                itemEditor.OnInspectorGUI();
            }

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    void DrawModelInfoSection()
    {
        EditorGUILayout.BeginVertical("box");
        showModelInfo = EditorGUILayout.Foldout(showModelInfo, "Model Information", true, EditorStyles.foldoutHeader);
        if (showModelInfo)
        {
            EditorGUILayout.BeginVertical("helpBox");

            if (currentItem.GetPrefab() != null)
            {
                EditorGUILayout.LabelField("Current Model Data:", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"Prefab: {currentItem.GetPrefab().name}");
                EditorGUILayout.LabelField($"Center Offset: {currentItem.GetCenterOffset()}");
                EditorGUILayout.LabelField($"Default Rotation: {currentItem.GetDefaultRotation()}");
                EditorGUILayout.LabelField($"Intrinsic Forward: {currentItem.GetIntrinsicForward()}");

                EditorGUILayout.Space();

                // Model analysis
                if (previewInstance != null)
                {
                    Bounds bounds = CalculateBounds(previewInstance);
                    EditorGUILayout.LabelField("Model Analysis:", EditorStyles.boldLabel);
                    EditorGUILayout.LabelField($"Bounds Size: {bounds.size}");
                    EditorGUILayout.LabelField($"Bounds Center: {bounds.center}");

                    Renderer[] renderers = previewInstance.GetComponentsInChildren<Renderer>();
                    EditorGUILayout.LabelField($"Renderers: {renderers.Length}");
                }
            }
            else
            {
                EditorGUILayout.HelpBox("No prefab assigned. Assign a prefab in the Item Properties section above.", MessageType.Warning);
            }

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    void DrawMeshBoundsSphere(Camera previewCamera)
    {
        if (Event.current.type != EventType.Repaint || currentItem == null) return;
        
        Handles.SetCamera(previewCamera);
        
        Color originalHandlesColor = Handles.color;
        Matrix4x4 originalHandlesMatrix = Handles.matrix;
        
        Quaternion previewModelRotation = Quaternion.Euler(currentItem.GetDefaultRotation());
        Vector3 sphereCenter = previewModelRotation * currentItem.GetCenterOffset();
        
        // Calcular raio
        float radius = currentItem.modelInfo.useAutoBounds ? 
            CalculateBoundsRadius(previewInstance) : 
            currentItem.modelInfo.meshBoundsRadius;
        
        // Desenhar esfera de limites
        Handles.color = Color.yellow;
        Handles.matrix = Matrix4x4.identity;
        Handles.DrawWireDisc(sphereCenter, Vector3.up, radius);
        Handles.DrawWireDisc(sphereCenter, Vector3.forward, radius);
        Handles.DrawWireDisc(sphereCenter, Vector3.right, radius);
        
        Handles.color = originalHandlesColor;
        Handles.matrix = originalHandlesMatrix;
    }

    float CalculateBoundsRadius(GameObject go)
    {
        Bounds bounds = CalculateBounds(go);
        return bounds.extents.magnitude;
    }

    void DrawIndicatorSettingsSection()
    {
        EditorGUILayout.BeginVertical("box");
        showIndicatorSettings = EditorGUILayout.Foldout(showIndicatorSettings, "Visual Indicators", true, EditorStyles.foldoutHeader);
        if (showIndicatorSettings)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.LabelField("Forward Direction Indicator:", EditorStyles.boldLabel);
            forwardIndicatorColor = EditorGUILayout.ColorField("Indicator Color", forwardIndicatorColor);
            forwardIndicatorDistance = EditorGUILayout.FloatField("Indicator Distance", forwardIndicatorDistance);
            forwardIndicatorGizmoSize = EditorGUILayout.FloatField("Indicator Gizmo Size", forwardIndicatorGizmoSize);

            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Center Point Indicator:", EditorStyles.boldLabel);
            showCenterIndicator = EditorGUILayout.Toggle("Show Center Cube", showCenterIndicator);
            if (showCenterIndicator)
            {
                centerIndicatorColor = EditorGUILayout.ColorField("Center Cube Color", centerIndicatorColor);
                centerIndicatorSize = EditorGUILayout.FloatField("Center Cube Size", centerIndicatorSize);
            }

            EditorGUILayout.Space();
            EditorGUILayout.HelpBox("Visual indicators help you understand the model's orientation and center point in 3D space.", MessageType.Info);

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    void DrawPreviewControlsSection()
    {
        EditorGUILayout.BeginVertical("box");
        showPreviewControls = EditorGUILayout.Foldout(showPreviewControls, "Preview Controls & Tools", true, EditorStyles.foldoutHeader);
        if (showPreviewControls)
        {
            EditorGUILayout.BeginVertical("helpBox");

            EditorGUILayout.LabelField("Camera Controls:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField("• Mouse Drag: Orbit around model", EditorStyles.miniLabel);
            EditorGUILayout.LabelField("• Scroll Wheel: Zoom in/out", EditorStyles.miniLabel);

            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Current Camera:", EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Distance: {cameraDistance:F2}");
            EditorGUILayout.LabelField($"Angles: Pitch {cameraOrbitAngles.x:F1}°, Yaw {cameraOrbitAngles.y:F1}°");

            EditorGUILayout.Space();

            EditorGUILayout.LabelField("Auto-Calculation Tools:", EditorStyles.boldLabel);

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Center Offset Calculation:", EditorStyles.miniLabel);

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Precise Center\n(Vertex Analysis)", GUILayout.Height(35)))
            {
                AutoCalculateModelCenterOffset();
            }
            if (GUILayout.Button("Reset to Zero", GUILayout.Height(35)))
            {
                ResetCenterOffset();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.HelpBox("Precise Center analyzes all vertices to find the true geometric center. Reset to Zero sets center offset to (0,0,0).", MessageType.Info);
            EditorGUILayout.EndVertical();

            EditorGUILayout.Space();

            EditorGUILayout.BeginVertical("box");
            EditorGUILayout.LabelField("Mesh Bounds Calculation:", EditorStyles.miniLabel);
            if (GUILayout.Button("Auto-Calculate Mesh Bounds", GUILayout.Height(25)))
            {
                AutoCalculateMeshBounds();
            }
            EditorGUILayout.HelpBox("Calculates the size of the mesh bounds for proper scaling.", MessageType.Info);
            EditorGUILayout.EndVertical();

            EditorGUILayout.BeginHorizontal();
            if (GUILayout.Button("Reset Camera"))
            {
                ResetCamera();
            }
            if (GUILayout.Button("Focus on Model"))
            {
                FocusOnModel();
            }
            EditorGUILayout.EndHorizontal();

            EditorGUILayout.EndVertical();
        }
        EditorGUILayout.EndVertical();
        EditorGUILayout.Space();
    }

    void DrawPreviewSection()
    {
        EditorGUILayout.LabelField("3D Model Preview", EditorStyles.boldLabel);

        Rect previewRect = GUILayoutUtility.GetRect(new GUIContent("Model Preview"), GUIStyle.none,
            GUILayout.MinWidth(100), GUILayout.MinHeight(150),
            GUILayout.ExpandWidth(true), GUILayout.ExpandHeight(true));

        if (currentItem.GetPrefab() != null && previewRenderUtility != null && previewInstance != null)
        {
            HandleCameraControls(previewRect);

            previewRenderUtility.BeginPreview(previewRect, GUIStyle.none);

            // Camera setup
            Quaternion lookRotation = Quaternion.Euler(cameraOrbitAngles.x, cameraOrbitAngles.y, 0);
            Vector3 cameraPosition = -(lookRotation * Vector3.forward * cameraDistance);
            previewRenderUtility.camera.transform.position = cameraPosition;
            previewRenderUtility.camera.transform.rotation = lookRotation;

            previewRenderUtility.Render(true);

            // Draw indicators
            DrawForwardIndicatorSphere(previewRenderUtility.camera);
            DrawCenterIndicatorCube(previewRenderUtility.camera);
            DrawMeshBoundsSphere(previewRenderUtility.camera); // Alterado para esfera

            Texture previewTexture = previewRenderUtility.EndPreview();
            GUI.DrawTexture(previewRect, previewTexture, ScaleMode.StretchToFill, false);

            // Preview info overlay
            DrawPreviewOverlay(previewRect);
        }
        else if (currentItem.GetPrefab() == null)
        {
            EditorGUI.LabelField(previewRect, "No Prefab Assigned", EditorStyles.centeredGreyMiniLabel);
            GUI.Box(previewRect, "", EditorStyles.helpBox);
        }
        else
        {
            EditorGUI.LabelField(previewRect, "Loading Preview...", EditorStyles.centeredGreyMiniLabel);
            GUI.Box(previewRect, "", EditorStyles.helpBox);
        }

        // Handle camera controls
        if (Event.current.type == EventType.MouseDrag && Event.current.button == 0 && previewRect.Contains(Event.current.mousePosition))
        {
            Repaint();
        }
    }

    void DrawPreviewOverlay(Rect previewRect)
    {
        // Draw overlay information
        Rect overlayRect = new Rect(previewRect.x + 5, previewRect.y + 5, 200, 60);
        GUI.Box(overlayRect, "", EditorStyles.helpBox);

        GUILayout.BeginArea(overlayRect);
        GUILayout.BeginVertical();

        GUILayout.Label("Preview Controls:", EditorStyles.miniLabel);
        GUILayout.Label("Drag: Orbit | Scroll: Zoom", EditorStyles.miniLabel);
        if (showCenterIndicator)
            GUILayout.Label("Cyan: Center | Yellow: Forward", EditorStyles.miniLabel);
        else
            GUILayout.Label("Yellow: Forward Direction", EditorStyles.miniLabel);

        GUILayout.EndVertical();
        GUILayout.EndArea();
    }

    void AutoCalculateMeshBounds()
    {
        if (currentItem == null || previewInstance == null)
        {
            Debug.LogWarning("Cannot calculate mesh bounds: No item or preview instance.");
            return;
        }
        
        float radius = CalculateBoundsRadius(previewInstance);
        if (radius <= 0f)
        {
            Debug.LogWarning("Cannot calculate mesh bounds: Invalid bounds.");
            return;
        }
        
        Undo.RecordObject(currentItem, "Auto-calculate Mesh Bounds Radius");
        currentItem.modelInfo.meshBoundsRadius = radius;
        EditorUtility.SetDirty(currentItem);
        Debug.Log($"Auto-calculated mesh bounds radius for '{currentItem.itemName}': {radius}");
        Repaint();
    }


    void ResetCamera()
    {
        cameraOrbitAngles = new Vector2(20f, 0f);
        if (previewInstance != null)
        {
            Bounds bounds = CalculateBounds(previewInstance);
            if (bounds.size != Vector3.zero)
            {
                 float objectRadius = bounds.extents.magnitude > 0 ? bounds.extents.magnitude : bounds.size.magnitude;
                float maxScaleComponent = Mathf.Max(currentItem.scaleRatio.x, currentItem.scaleRatio.y, currentItem.scaleRatio.z);
                cameraDistance = objectRadius * 3.0f / Mathf.Sin(previewRenderUtility.camera.fieldOfView * 0.5f * Mathf.Deg2Rad) * maxScaleComponent;
                cameraDistance = Mathf.Max(cameraDistance, 0.5f);
            }
            else
            {
                cameraDistance = 2.0f;
            }
        }
        else
        {
            cameraDistance = 2.0f;
        }
        Repaint();
    }

    void FocusOnModel()
    {
        if (previewInstance != null)
        {
            Bounds bounds = CalculateBounds(previewInstance);
            if (bounds.size != Vector3.zero)
            {
                float objectRadius = bounds.extents.magnitude > 0 ? bounds.extents.magnitude : bounds.size.magnitude;
                float maxScaleComponent = Mathf.Max(currentItem.scaleRatio.x, currentItem.scaleRatio.y, currentItem.scaleRatio.z);
                cameraDistance = objectRadius * 2.5f / Mathf.Sin(previewRenderUtility.camera.fieldOfView * 0.5f * Mathf.Deg2Rad) * maxScaleComponent;
                cameraDistance = Mathf.Max(cameraDistance, 0.1f);
                Repaint();
            }
        }
    }

    void HandleCameraControls(Rect previewRect)
    {
        Event e = Event.current;
        if (previewRect.Contains(e.mousePosition))
        {
            // Zoom com ScrollWheel
            if (e.type == EventType.ScrollWheel)
            {
                cameraDistance -= e.delta.y * 0.05f * cameraDistance;
                cameraDistance = Mathf.Max(0.1f, cameraDistance);
                e.Use();
            }
            // Orbitar com MouseDrag (botão esquerdo)
            else if (e.type == EventType.MouseDrag && e.button == 0)
            {
                cameraOrbitAngles.y += e.delta.x * 0.4f;
                cameraOrbitAngles.x -= e.delta.y * 0.4f;
                cameraOrbitAngles.x = Mathf.Clamp(cameraOrbitAngles.x, -89f, 89f); // Limita o pitch
                e.Use();
            }
        }
    }

    void DrawForwardIndicatorSphere(Camera previewCamera)
    {
        // O desenho com Handles deve ocorrer preferencialmente durante o evento Repaint.
        if (Event.current.type != EventType.Repaint)
        {
            // Log opcional para ver outros eventos, pode ser barulhento:
            // Debug.Log($"DrawForwardIndicatorSphere: Skipping, not Repaint event. Current event: {Event.current.type}");
            return;
        }

        // Debug.Log($"DrawForwardIndicatorSphere: ENTERING - Event: {Event.current.type}");

        if (previewInstance == null)
        {
            // Debug.LogWarning("DrawForwardIndicatorSphere: Skipping, previewInstance is null.");
            return;
        }
        if (currentItem == null)
        {
            // Debug.LogWarning("DrawForwardIndicatorSphere: Skipping, currentItem is null.");
            return;
        }
        if (currentItem.GetIntrinsicForward() == Vector3.zero)
        {
            // Debug.LogWarning($"DrawForwardIndicatorSphere: Skipping, modelIntrinsicForward is zero. Value: {currentItem.modelIntrinsicForward}");
            return;
        }

        Handles.SetCamera(previewCamera);

        Color originalHandlesColor = Handles.color;
        Matrix4x4 originalHandlesMatrix = Handles.matrix;

        if (forwardIndicatorGizmoSize <= 0f)
        {
            // Não retorna aqui para que a linha de teste ainda possa ser desenhada,
            // mas a esfera não será (ou será inválida).
            // Debug.LogWarning($"DrawForwardIndicatorSphere: forwardIndicatorGizmoSize is {forwardIndicatorGizmoSize}. Gizmo will be invisible or invalid.");
        }

        Handles.color = forwardIndicatorColor;
        float gizmoSizeToDraw = Mathf.Max(forwardIndicatorGizmoSize, 0.001f); // Garante um tamanho mínimo

        // Rotação aplicada ao modelo no preview
        Quaternion previewModelRotation = Quaternion.Euler(currentItem.GetDefaultRotation());
        
        // 1. Calcula o ponto de partida do indicador (o centro definido do modelo)
        // modelCenterOffset é local ao prefab. Transformamos para o espaço do mundo do preview.
        // Como o previewInstance está na origem e rotacionado, isso está correto.
        Vector3 indicatorOriginInPreviewWorld = previewModelRotation * currentItem.GetCenterOffset();

        // 2. Calcula a direção do indicador
        // modelIntrinsicForward é local ao prefab. Normaliza e rotaciona.
        Vector3 localForwardNormalized = currentItem.GetIntrinsicForward().normalized;
        Vector3 directionInPreviewWorld = previewModelRotation * localForwardNormalized;

        // 3. Calcula o ponto final do indicador (onde o gizmo estará)
        Vector3 indicatorEndPointInPreviewWorld = indicatorOriginInPreviewWorld + directionInPreviewWorld * forwardIndicatorDistance;
        
        // Debug.Log($"DrawForwardIndicatorSphere: Origin={indicatorOriginInPreviewWorld.ToString("F3")}, End={indicatorEndPointInPreviewWorld.ToString("F3")}, Direction={directionInPreviewWorld.ToString("F3")}");
        // Debug.Log($"DrawForwardIndicatorSphere: Preview cameraPos={previewCamera.transform.position.ToString("F3")}, cameraRot={previewCamera.transform.rotation.eulerAngles.ToString("F3")}, FoV={previewCamera.fieldOfView}, NearClip={previewCamera.nearClipPlane}, FarClip={previewCamera.farClipPlane}");

        Handles.matrix = Matrix4x4.identity; // Ensure drawing in preview world space

        // Desenha a linha da origem calculada até o ponto final
        Handles.DrawLine(indicatorOriginInPreviewWorld, indicatorEndPointInPreviewWorld);
        
        // Desenha um cubo aramado no final da linha
        if (gizmoSizeToDraw > 0f) 
        {
            // Handles.DrawWireCube espera o centro e o tamanho total (Vector3) do cubo.
            Handles.DrawWireCube(indicatorEndPointInPreviewWorld, Vector3.one * gizmoSizeToDraw);
            // Debug.Log("DrawForwardIndicatorSphere: DrawWireCube CALLED.");
        }
        
        Handles.color = originalHandlesColor;
        Handles.matrix = originalHandlesMatrix;
    }

    void DrawCenterIndicatorCube(Camera previewCamera)
    {
        if (!showCenterIndicator || Event.current.type != EventType.Repaint || centerIndicatorSize <= 0f)
        {
            return;
        }

        if (previewInstance == null || currentItem == null)
        {
            return;
        }

        Handles.SetCamera(previewCamera);

        Color originalHandlesColor = Handles.color;
        Matrix4x4 originalHandlesMatrix = Handles.matrix;

        Handles.color = centerIndicatorColor;

        // A rotação do modelo no preview
        Quaternion previewModelRotation = Quaternion.Euler(currentItem.GetDefaultRotation());

        // O modelCenterOffset é local ao prefab. Precisamos rotacioná-lo
        // para o espaço do mundo do preview (onde o previewInstance está rotacionado).
        // A posição do previewInstance é (0,0,0) na cena do preview.
        Vector3 cubePositionInPreviewWorld = previewModelRotation * currentItem.GetCenterOffset();

        // Debug.Log($"DrawCenterIndicatorCube: Pos={cubePositionInPreviewWorld}, Size={centerIndicatorSize}, Offset={currentItem.modelCenterOffset}");

        Handles.matrix = Matrix4x4.identity; // Desenha no espaço do mundo do preview

        // Handles.CubeHandleCap desenha um cubo sólido. Para um cubo aramado:
        Handles.DrawWireCube(cubePositionInPreviewWorld, Vector3.one * centerIndicatorSize);

        Handles.color = originalHandlesColor;
        Handles.matrix = originalHandlesMatrix;
    }

    void AutoCalculateModelCenterOffset()
    {
        if (currentItem == null)
        {
            Debug.LogWarning("[ItemModelPreviewWindow] Cannot auto-calculate center: No ItemSO selected.");
            return;
        }
        if (currentItem.GetPrefab() == null)
        {
            Debug.LogWarning($"[ItemModelPreviewWindow] Cannot auto-calculate center for '{currentItem.itemName}': Prefab is not assigned.");
            return;
        }

        // Use the accurate vertex-based calculation method
        Vector3 calculatedCenter = CalculateAccurateMeshCenter(currentItem.GetPrefab());

        if (calculatedCenter == Vector3.positiveInfinity)
        {
            Debug.LogWarning($"[ItemModelPreviewWindow] Cannot auto-calculate center for '{currentItem.itemName}': No valid mesh data found.");
            return;
        }

        Undo.RecordObject(currentItem, "Auto-calculate Model Center Offset");
        currentItem.modelInfo.centerOffset = calculatedCenter;
        EditorUtility.SetDirty(currentItem); // Mark the ItemSO as changed
        Debug.Log($"[ItemModelPreviewWindow] Auto-calculated precise modelCenterOffset for '{currentItem.itemName}' to: {calculatedCenter}");
        Repaint(); // Repaint the window to show updated values and gizmos
    }

    /// <summary>
    /// Calculates the accurate geometric center of all meshes in the prefab by analyzing all vertices
    /// </summary>
    /// <param name="prefab">The prefab to analyze</param>
    /// <returns>The calculated center in prefab root space, or Vector3.positiveInfinity if no valid meshes found</returns>
    Vector3 CalculateAccurateMeshCenter(GameObject prefab)
    {
        // Get all MeshFilter components in the prefab and its children
        MeshFilter[] filters = prefab.GetComponentsInChildren<MeshFilter>(true);

        // Also check for SkinnedMeshRenderer components for animated meshes
        SkinnedMeshRenderer[] skinnedRenderers = prefab.GetComponentsInChildren<SkinnedMeshRenderer>(true);

        if (filters.Length == 0 && skinnedRenderers.Length == 0)
        {
            Debug.LogWarning($"[ItemModelPreviewWindow] No mesh components found in prefab '{prefab.name}'");
            return Vector3.positiveInfinity;
        }

        Vector3 min = Vector3.positiveInfinity;
        Vector3 max = Vector3.negativeInfinity;
        int totalVerticesProcessed = 0;

        // Process regular MeshFilter components
        foreach (var filter in filters)
        {
            if (filter.sharedMesh == null) continue;

            Vector3[] vertices = filter.sharedMesh.vertices;
            Transform meshTransform = filter.transform;

            foreach (Vector3 vertex in vertices)
            {
                // Transform vertex from mesh local space to world space, then to prefab root space
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 prefabLocalPoint = prefab.transform.InverseTransformPoint(worldPoint);

                // Track the absolute minimum and maximum coordinates
                min = Vector3.Min(min, prefabLocalPoint);
                max = Vector3.Max(max, prefabLocalPoint);
                totalVerticesProcessed++;
            }
        }

        // Process SkinnedMeshRenderer components (for animated/rigged meshes)
        foreach (var skinnedRenderer in skinnedRenderers)
        {
            if (skinnedRenderer.sharedMesh == null) continue;

            Vector3[] vertices = skinnedRenderer.sharedMesh.vertices;
            Transform meshTransform = skinnedRenderer.transform;

            foreach (Vector3 vertex in vertices)
            {
                // Transform vertex from mesh local space to world space, then to prefab root space
                Vector3 worldPoint = meshTransform.TransformPoint(vertex);
                Vector3 prefabLocalPoint = prefab.transform.InverseTransformPoint(worldPoint);

                // Track the absolute minimum and maximum coordinates
                min = Vector3.Min(min, prefabLocalPoint);
                max = Vector3.Max(max, prefabLocalPoint);
                totalVerticesProcessed++;
            }
        }

        min = new Vector3(min.x * prefab.transform.localScale.x, min.y * prefab.transform.localScale.y, min.z * prefab.transform.localScale.z);
        max = new Vector3(max.x * prefab.transform.localScale.x, max.y * prefab.transform.localScale.y, max.z * prefab.transform.localScale.z);

        // Validate that we found valid bounds
        if (min == Vector3.positiveInfinity || max == Vector3.negativeInfinity)
        {
            Debug.LogWarning($"[ItemModelPreviewWindow] Could not calculate valid bounds for prefab '{prefab.name}'");
            return Vector3.positiveInfinity;
        }

        // Calculate the actual geometric center
        Vector3 geometricCenter = (min + max) * 0.5f;

        Debug.Log($"[ItemModelPreviewWindow] Calculated precise center for '{prefab.name}' - " +
                  $"Min: {min}, Max: {max}, Center: {geometricCenter}, Vertices: {totalVerticesProcessed}");

        return geometricCenter;
    }

    /// <summary>
    /// Resets the center offset to zero
    /// </summary>
    void ResetCenterOffset()
    {
        if (currentItem == null)
        {
            Debug.LogWarning("[ItemModelPreviewWindow] Cannot reset center offset: No ItemSO selected.");
            return;
        }

        Undo.RecordObject(currentItem, "Reset Model Center Offset");
        currentItem.modelInfo.centerOffset = Vector3.zero;
        EditorUtility.SetDirty(currentItem);
        Debug.Log($"[ItemModelPreviewWindow] Reset modelCenterOffset for '{currentItem.itemName}' to zero.");
        Repaint();
    }
}