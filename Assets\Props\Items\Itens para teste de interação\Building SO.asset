%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c5db3e374ca939948ad2cf8aad56acdc, type: 3}
  m_Name: Building SO
  m_EditorClassIdentifier: 
  ID: 0
  itemName: 
  description: 
  icon: {fileID: 0}
  modelInfo:
    prefab: {fileID: 4564285227467650946, guid: 57c87aada7af075408a599328b7697ab, type: 3}
    defaultRotation: {x: 0, y: -90, z: 0}
    intrinsicForward: {x: -1, y: 0, z: 0}
    centerOffset: {x: -0.00077593327, y: 0.81950295, z: -0.07196379}
    scaleRatio: {x: 1, y: 1, z: 1}
    meshBoundsRadius: 5.974415
    useAutoBounds: 0
  stack: 0
  maxStack: 0
  value: 0
  weight: 0
  gridWidth: 1
  gridHeight: 1
  canBeRotated: 0
